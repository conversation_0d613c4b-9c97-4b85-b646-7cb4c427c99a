<?php
/**
 * The header for policy pages
 *
 * This is a simplified header template that only displays the logo centered at the top
 * without any navigation menu, specifically for privacy policy and cookie policy pages.
 *
 * @package NextNaked
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">

	<?php wp_head(); ?>
    <style>
        /* Custom styles for policy pages header */
        .policy-header {
            background-color: #2B2D37;
            padding: 30px 0 15px 0; /* Ridotto il padding inferiore */
            text-align: center;
            background-image: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/ruler.webp');
            background-repeat: repeat-x;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2); /* Linea divisoria leggermente più visibile */
            margin-bottom: 0;
        }

        /* Animazione WOW per il logo */
        @keyframes logoWowEffect {
            0% {
                transform: scale(1);
                filter: brightness(1) drop-shadow(0 0 0px rgba(255, 255, 255, 0));
            }
            50% {
                transform: scale(1.05);
                filter: brightness(1.3) drop-shadow(0 0 8px rgba(255, 255, 255, 0.4));
            }
            100% {
                transform: scale(1);
                filter: brightness(1) drop-shadow(0 0 0px rgba(255, 255, 255, 0));
            }
        }

        .policy-logo {
            max-width: 300px; /* Aumentato da 200px a 300px */
            margin: 0 auto;
            padding: 15px 0;
            animation: logoWowEffect 3s ease-in-out infinite; /* Effetto WOW in loop */
        }

        .policy-logo img {
            max-width: 100%;
            height: auto;
            filter: brightness(0) saturate(100%) invert(100%);
            -webkit-filter: brightness(0) saturate(100%) invert(100%);
        }

        .custom-logo-policy {
            width: 300px !important; /* Forza la larghezza del logo */
            height: auto !important; /* Mantiene le proporzioni */
            max-width: 100% !important;
        }

        @media (max-width: 768px) {
            .custom-logo-policy {
                width: 220px !important; /* Dimensione ridotta per mobile */
            }
        }

        /* Rimosso l'effetto hover poiché ora abbiamo l'animazione continua */

        @media (max-width: 768px) {
            .policy-header {
                padding: 25px 0 10px 0; /* Ridotto il padding inferiore */
            }

            .policy-logo {
                max-width: 220px; /* Aumentato da 160px a 220px */
                animation: logoWowEffect 2.5s ease-in-out infinite; /* Animazione leggermente più veloce su mobile */
            }

            /* Animazione leggermente più sottile per mobile */
            @keyframes logoWowEffect {
                0% {
                    transform: scale(1);
                    filter: brightness(1) drop-shadow(0 0 0px rgba(255, 255, 255, 0));
                }
                50% {
                    transform: scale(1.03); /* Scala ridotta su mobile */
                    filter: brightness(1.2) drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
                }
                100% {
                    transform: scale(1);
                    filter: brightness(1) drop-shadow(0 0 0px rgba(255, 255, 255, 0));
                }
            }
        }
    </style>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<div id="page" class="site">
	<a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e( 'Skip to content', 'nextnaked' ); ?></a>

	<!-- Simplified header for policy pages -->
    <div class="policy-header">
        <div class="container">
            <div class="policy-logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php
                    if (has_custom_logo()) {
                        // Ottieni l'ID del logo personalizzato
                        $custom_logo_id = get_theme_mod('custom_logo');
                        // Ottieni l'URL del logo
                        $logo_url = wp_get_attachment_image_url($custom_logo_id, 'full');
                        // Ottieni l'alt text del logo
                        $logo_alt = get_post_meta($custom_logo_id, '_wp_attachment_image_alt', true);
                        if (empty($logo_alt)) {
                            $logo_alt = get_bloginfo('name');
                        }
                        // Mostra il logo con dimensioni maggiori
                        echo '<img src="' . esc_url($logo_url) . '" class="custom-logo-policy" alt="' . esc_attr($logo_alt) . '">';
                    } else {
                        echo '<img width="250" height="auto" src="' . esc_url(get_template_directory_uri()) . '/assets/img/images/pacitto_movimento_terra.webp" class="img-fluid" alt="Logo Pacitto Movimento Terra">';
                    }
                    ?>
                </a>
            </div>
        </div>
    </div>
