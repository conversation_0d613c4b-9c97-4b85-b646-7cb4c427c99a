/* 404 Page Styles */

/* Breadcrumb styles */
.error-404-breadcrumb {
    background-color: #2B2D37;
    padding: 100px 0 30px 0;
    margin-bottom: 0;
    position: relative;
}

.error-404-breadcrumb::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(43, 45, 55, 0.8);
    z-index: 1;
}

.error-404-breadcrumb .container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.error-404-breadcrumb h2 {
    color: #fff;
    font-size: 32px;
    margin-bottom: 10px;
    font-weight: 600;
}

.error-404-breadcrumb .breadcrumb-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.error-404-breadcrumb .breadcrumb-item {
    color: #FABC3D;
    font-size: 16px;
}

.error-404-breadcrumb .breadcrumb-item a {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.error-404-breadcrumb .breadcrumb-item a:hover {
    color: #FABC3D;
}

.error-404-breadcrumb .breadcrumb-item + .breadcrumb-item:before {
    content: "/";
    padding: 0 10px;
    color: #fff;
}

/* 404 Content styles */
.error-404-content {
    padding: 80px 0;
    background-color: #f9f9f9;
    color: #2B2D37;
}

.error-404-content .container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    text-align: center;
}

.error-404-content .error-code {
    font-size: 120px;
    font-weight: 700;
    color: #FABC3D;
    line-height: 1;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-404-content .error-title {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2B2D37;
}

.error-404-content .error-description {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.error-404-content .error-image {
    max-width: 300px;
    margin: 0 auto 30px;
}

.error-404-content .error-image img {
    width: 100%;
    height: auto;
}

/* CTA Button */
.error-404-content .home-button {
    display: inline-block;
    font-size: 18px;
    font-weight: 700;
    padding: 15px 30px;
    background-color: #FABC3D;
    color: #2B2D37;
    border: none;
    border-radius: 3px;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.error-404-content .home-button:hover {
    background-color: #2B2D37;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Search form styling */
.error-404-content .search-form {
    max-width: 500px;
    margin: 0 auto 30px;
}

.error-404-content .search-form label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.error-404-content .search-form .search-field {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 16px;
    margin-bottom: 15px;
}

.error-404-content .search-form .search-submit {
    background-color: #2B2D37;
    color: #fff;
    border: none;
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.error-404-content .search-form .search-submit:hover {
    background-color: #FABC3D;
    color: #2B2D37;
}

/* Helpful links section */
.error-404-content .helpful-links {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}

.error-404-content .helpful-links h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #2B2D37;
}

.error-404-content .helpful-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

.error-404-content .helpful-links li {
    margin-bottom: 10px;
}

.error-404-content .helpful-links a {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f5f5f5;
    color: #2B2D37;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.error-404-content .helpful-links a:hover {
    background-color: #FABC3D;
    color: #2B2D37;
}

/* Responsive styles */
@media (max-width: 768px) {
    .error-404-content {
        padding: 40px 0;
    }

    .error-404-content .container {
        padding: 30px 20px;
    }

    .error-404-content .error-code {
        font-size: 100px;
    }

    .error-404-content .error-title {
        font-size: 28px;
    }

    .error-404-content .error-description {
        font-size: 16px;
    }

    .error-404-breadcrumb {
        padding: 80px 0 20px;
    }

    .error-404-breadcrumb h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .error-404-content .error-code {
        font-size: 80px;
    }

    .error-404-content .error-title {
        font-size: 24px;
    }

    .error-404-content .home-button {
        padding: 12px 25px;
        font-size: 16px;
    }
}
