/**
 * Onoranze Funebri Ignagni - Main JavaScript
 * Modern interactions and animations
 */

(function($) {
    'use strict';

    // ===== GLOBAL VARIABLES =====
    let isLoading = true;
    let scrollPosition = 0;
    let ticking = false;

    // ===== DOM READY =====
    $(document).ready(function() {
        initializeApp();
    });

    // ===== WINDOW LOAD =====
    $(window).on('load', function() {
        hideLoadingScreen();
    });

    // ===== INITIALIZE APP =====
    function initializeApp() {
        // Initialize all components
        initLoadingScreen();
        initNavigation();
        initScrollEffects();
        initHeroAnimations();
        initFormValidation();
        initBackToTop();
        initCookieBanner();
        initSmoothScrolling();
        initParallaxEffects();
        initMobileMenu();

        // Set body class for loading state
        $('body').addClass('loading');

        console.log('Onoranze Funebri Ignagni - Landing Page Initialized');
    }

    // ===== LOADING SCREEN =====
    function initLoadingScreen() {
        const loadingScreen = $('#loading-screen');

        // Simulate minimum loading time for better UX
        setTimeout(function() {
            if (!isLoading) {
                hideLoadingScreen();
            }
        }, 1500);
    }

    function hideLoadingScreen() {
        isLoading = false;
        const loadingScreen = $('#loading-screen');

        loadingScreen.addClass('hidden');
        $('body').removeClass('loading');

        // Remove loading screen from DOM after animation
        setTimeout(function() {
            loadingScreen.remove();
            triggerHeroAnimations();
        }, 500);
    }

    // ===== NAVIGATION =====
    function initNavigation() {
        const nav = $('.main-navigation');
        const navToggle = $('.nav-toggle');
        const navMenu = $('.nav-menu');
        const navLinks = $('.nav-link');
        const headerArea = $('.header-area, .mobile-header');

        // Make header visible immediately on page load
        headerArea.addClass('visible');

        // Show navigation after scroll and add scrolled class
        $(window).on('scroll', throttle(function() {
            const scrollTop = $(window).scrollTop();

            if (scrollTop > 100) {
                nav.addClass('visible');
                headerArea.addClass('scrolled');
                // Add sticky class for the existing theme sticky menu
                $('.header-sticky').addClass('is-sticky');
                // Add body class for sticky menu padding and navigation visibility
                $('body').addClass('sticky-menu-active navigation-visible');
            } else {
                nav.removeClass('visible');
                headerArea.removeClass('scrolled');
                // Remove sticky class
                $('.header-sticky').removeClass('is-sticky');
                // Remove body class
                $('body').removeClass('sticky-menu-active navigation-visible');
            }
        }, 10));

        // Mobile menu toggle
        navToggle.on('click', function() {
            $(this).toggleClass('active');
            navMenu.toggleClass('active');
        });

        // Close mobile menu on link click
        navLinks.on('click', function() {
            navToggle.removeClass('active');
            navMenu.removeClass('active');
        });

        // Active link highlighting
        $(window).on('scroll', throttle(function() {
            updateActiveNavLink();
        }, 100));
    }

    function updateActiveNavLink() {
        const scrollTop = $(window).scrollTop();
        const sections = $('section[id]');

        sections.each(function() {
            const section = $(this);
            const sectionId = section.attr('id');

            // Use smaller offset for hero section since menu overlays
            const offset = sectionId === 'home' ? 50 : 80;

            const sectionTop = section.offset().top - offset;
            const sectionBottom = sectionTop + section.outerHeight();

            if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
                // Remove active class from all menu links (including centered menu)
                $('.nav-link, .main-nav-menu a, #primary-menu a, .menu-items-left a, .menu-items-right a').removeClass('active');
                $('.menu-items-left li, .menu-items-right li').removeClass('current-menu-item');

                // Add active class to current section link
                $(`.nav-link[href="#${sectionId}"], .main-nav-menu a[href="#${sectionId}"], #primary-menu a[href="#${sectionId}"], .menu-items-left a[href="#${sectionId}"], .menu-items-right a[href="#${sectionId}"]`).addClass('active');
                $(`.menu-items-left a[href="#${sectionId}"], .menu-items-right a[href="#${sectionId}"]`).parent().addClass('current-menu-item');
            }
        });
    }

    // ===== SCROLL EFFECTS =====
    function initScrollEffects() {
        $(window).on('scroll', throttle(function() {
            updateScrollProgress();
            handleScrollAnimations();
        }, 10));
    }

    function updateScrollProgress() {
        const scrollTop = $(window).scrollTop();
        const docHeight = $(document).height() - $(window).height();
        const scrollPercent = (scrollTop / docHeight) * 100;

        $('.progress-bar').css('width', scrollPercent + '%');
    }

    function handleScrollAnimations() {
        // Add scroll-based animations here
        const scrollTop = $(window).scrollTop();

        // Parallax effect removed for hero background to prevent zoom issues
        // Hero background now remains static for better visual stability
    }

    // ===== HERO ANIMATIONS =====
    function initHeroAnimations() {
        // Initialize hero particles animation
        createParticleEffect();
    }

    function triggerHeroAnimations() {
        // Trigger hero animations after loading screen
        $('.hero-title').addClass('animate');
        $('.hero-subtitle').addClass('animate');
        $('.hero-actions').addClass('animate');
        $('.hero-features').addClass('animate');
        $('.hero-scroll-indicator').addClass('animate');
    }

    function createParticleEffect() {
        // Enhanced particle effect using CSS animations
        const particles = $('.hero-particles');

        // Add dynamic particle movement
        setInterval(function() {
            const randomX = Math.random() * 100;
            const randomY = Math.random() * 100;
            particles.css('background-position', `${randomX}% ${randomY}%`);
        }, 3000);
    }

    // ===== SMOOTH SCROLLING =====
    function initSmoothScrolling() {
        // Handle anchor links in WordPress menu, custom navigation, and centered menu
        $(document).on('click', 'a[href^="#"], .main-nav-menu a[href^="#"], #primary-menu a[href^="#"], .menu-items-left a[href^="#"], .menu-items-right a[href^="#"]', function(e) {
            const href = this.getAttribute('href');

            // Skip if it's just a hash without target
            if (href === '#' || href === '#!') {
                return;
            }

            e.preventDefault();
            const target = $(href);

            if (target.length) {
                // For hero section, scroll to exact position (menu will overlay)
                // For other sections, account for menu height
                const offset = target.attr('id') === 'home' ? 0 : 80;
                const offsetTop = target.offset().top - offset;

                $('html, body').animate({
                    scrollTop: offsetTop
                }, 800, 'easeInOutQuart');

                // Update active state immediately for all menu types
                $('.nav-link, .main-nav-menu a, #primary-menu a, .menu-items-left a, .menu-items-right a').removeClass('active');
                $('.menu-items-left li, .menu-items-right li').removeClass('current-menu-item');
                $(this).addClass('active');
                $(this).parent().addClass('current-menu-item');
            }
        });
    }

    // ===== FORM VALIDATION =====
    function initFormValidation() {
        const form = $('#contact-form');
        const submitBtn = $('.btn-submit');

        form.on('submit', function(e) {
            e.preventDefault();

            if (validateForm()) {
                submitForm();
            }
        });

        // Real-time validation
        form.find('input, select, textarea').on('blur', function() {
            validateField($(this));
        });

        // Phone number formatting
        $('#phone').on('input', function() {
            formatPhoneNumber($(this));
        });
    }

    function validateForm() {
        let isValid = true;
        const form = $('#contact-form');

        // Validate required fields
        form.find('[required]').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });

        // Validate email format
        const email = $('#email');
        if (email.val() && !isValidEmail(email.val())) {
            showFieldError(email, 'Inserisci un indirizzo email valido');
            isValid = false;
        }

        // Validate phone format
        const phone = $('#phone');
        if (!isValidPhone(phone.val())) {
            showFieldError(phone, 'Inserisci un numero di telefono valido');
            isValid = false;
        }

        return isValid;
    }

    function validateField(field) {
        const value = field.val().trim();
        const fieldName = field.attr('name');

        // Clear previous errors
        hideFieldError(field);

        // Check required fields
        if (field.prop('required') && !value) {
            showFieldError(field, 'Questo campo è obbligatorio');
            return false;
        }

        // Specific validations
        switch (fieldName) {
            case 'name':
                if (value.length < 2) {
                    showFieldError(field, 'Il nome deve contenere almeno 2 caratteri');
                    return false;
                }
                break;

            case 'email':
                if (value && !isValidEmail(value)) {
                    showFieldError(field, 'Inserisci un indirizzo email valido');
                    return false;
                }
                break;

            case 'phone':
                if (!isValidPhone(value)) {
                    showFieldError(field, 'Inserisci un numero di telefono valido');
                    return false;
                }
                break;
        }

        return true;
    }

    function showFieldError(field, message) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup.find('.form-error');

        formGroup.addClass('error');
        errorElement.text(message);
    }

    function hideFieldError(field) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup.find('.form-error');

        formGroup.removeClass('error');
        errorElement.text('');
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidPhone(phone) {
        // Italian phone number validation
        const phoneRegex = /^(\+39|0039|39)?[\s]?([0-9]{2,3}[\s]?[0-9]{6,7}|[0-9]{3}[\s]?[0-9]{7,8}|[0-9]{10,11})$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }

    function formatPhoneNumber(field) {
        let value = field.val().replace(/\D/g, '');

        // Format as Italian phone number
        if (value.length > 0) {
            if (value.startsWith('39')) {
                value = '+' + value;
            } else if (value.length === 10) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
            }
        }

        field.val(value);
    }

    function submitForm() {
        const form = $('#contact-form');
        const submitBtn = $('.btn-submit');
        const formData = new FormData(form[0]);

        // Show loading state
        submitBtn.addClass('loading').prop('disabled', true);

        // Simulate form submission (replace with actual endpoint)
        setTimeout(function() {
            // Success simulation
            showFormSuccess();
            form[0].reset();

            // Reset button state
            submitBtn.removeClass('loading').prop('disabled', false);
        }, 2000);
    }

    function showFormSuccess() {
        // Create success message
        const successMessage = $(`
            <div class="form-success" style="
                background: #27ae60;
                color: white;
                padding: 1rem;
                border-radius: 8px;
                margin-bottom: 1rem;
                text-align: center;
            ">
                <i class="fas fa-check-circle"></i>
                Grazie per averci contattato! Ti risponderemo al più presto.
            </div>
        `);

        $('#contact-form').prepend(successMessage);

        // Remove success message after 5 seconds
        setTimeout(function() {
            successMessage.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // ===== BACK TO TOP BUTTON =====
    function initBackToTop() {
        const backToTopBtn = $('#back-to-top');

        $(window).on('scroll', throttle(function() {
            const scrollTop = $(window).scrollTop();

            if (scrollTop > 300) {
                backToTopBtn.addClass('visible');
            } else {
                backToTopBtn.removeClass('visible');
            }
        }, 100));

        backToTopBtn.on('click', function() {
            $('html, body').animate({
                scrollTop: 0
            }, 800, 'easeInOutCubic');
        });
    }

    // ===== COOKIE BANNER =====
    function initCookieBanner() {
        const cookieBanner = $('#cookie-banner');
        const acceptBtn = $('#cookie-accept');
        const settingsBtn = $('#cookie-settings');

        // Check if cookies were already accepted
        if (!localStorage.getItem('cookiesAccepted')) {
            setTimeout(function() {
                cookieBanner.addClass('visible');
            }, 2000);
        }

        acceptBtn.on('click', function() {
            localStorage.setItem('cookiesAccepted', 'true');
            cookieBanner.removeClass('visible');
        });

        settingsBtn.on('click', function() {
            // Open cookie settings modal (implement as needed)
            alert('Impostazioni cookie - Da implementare');
        });
    }

    // ===== PARALLAX EFFECTS =====
    function initParallaxEffects() {
        $(window).on('scroll', throttle(function() {
            const scrollTop = $(window).scrollTop();

            // Hero parallax removed to prevent zoom issues on page load
            // Hero background now remains static for better visual stability

            // Service cards subtle parallax
            $('.service-card').each(function(index) {
                const offset = scrollTop * (0.1 + index * 0.02);
                $(this).css('transform', `translateY(${offset}px)`);
            });
        }, 16));
    }

    // ===== MOBILE MENU =====
    function initMobileMenu() {
        const mobileMenuTrigger = $('#mobile-menu-trigger');
        const mobileMenuClose = $('#mobile-menu-close-trigger');
        const mobileMenuOverlay = $('#mobile-menu-overlay');
        const offcanvasMenu = $('.offcanvas-mobile-menu');
        const body = $('body');

        // Open mobile menu
        mobileMenuTrigger.on('click', function(e) {
            e.preventDefault();
            $(this).find('.mobile-menu-trigger').addClass('active');
            offcanvasMenu.addClass('active');
            mobileMenuOverlay.addClass('active');
            body.addClass('mobile-menu-open');

            // Create overlay if it doesn't exist
            if (!$('.mobile-menu-overlay').length) {
                $('body').append('<div class="mobile-menu-overlay"></div>');
            }
            $('.mobile-menu-overlay').addClass('active');
        });

        // Close mobile menu
        function closeMobileMenu() {
            mobileMenuTrigger.find('.mobile-menu-trigger').removeClass('active');
            offcanvasMenu.removeClass('active');
            mobileMenuOverlay.removeClass('active');
            $('.mobile-menu-overlay').removeClass('active');
            body.removeClass('mobile-menu-open');
        }

        // Close menu on close button click
        mobileMenuClose.on('click', function(e) {
            e.preventDefault();
            closeMobileMenu();
        });

        // Close menu on overlay click
        $(document).on('click', '.mobile-menu-overlay', function() {
            closeMobileMenu();
        });

        // Close menu on outside click
        $(document).on('click', function(e) {
            if (!offcanvasMenu.is(e.target) &&
                offcanvasMenu.has(e.target).length === 0 &&
                !mobileMenuTrigger.is(e.target) &&
                mobileMenuTrigger.has(e.target).length === 0) {
                closeMobileMenu();
            }
        });

        // Handle submenu toggles in mobile menu
        $('.offcanvas-navigation .menu-item-has-children > a').on('click', function(e) {
            e.preventDefault();
            const parentLi = $(this).parent();
            const submenu = parentLi.find('.sub-menu');

            parentLi.toggleClass('active');

            if (parentLi.hasClass('active')) {
                submenu.slideDown(300);
            } else {
                submenu.slideUp(300);
            }
        });

        // Close menu on window resize if desktop
        $(window).on('resize', function() {
            if ($(window).width() > 991) {
                closeMobileMenu();
            }
        });

        // Handle orientation change
        $(window).on('orientationchange', function() {
            setTimeout(function() {
                if ($(window).width() > 991) {
                    closeMobileMenu();
                }
            }, 100);
        });

        // Prevent body scroll when menu is open
        function toggleBodyScroll(disable) {
            if (disable) {
                body.css({
                    'overflow': 'hidden',
                    'position': 'fixed',
                    'width': '100%'
                });
            } else {
                body.css({
                    'overflow': '',
                    'position': '',
                    'width': ''
                });
            }
        }

        // Update body scroll when menu opens/closes
        mobileMenuTrigger.on('click', function() {
            setTimeout(function() {
                toggleBodyScroll(offcanvasMenu.hasClass('active'));
            }, 10);
        });

        mobileMenuClose.on('click', function() {
            toggleBodyScroll(false);
        });

        $(document).on('click', '.mobile-menu-overlay', function() {
            toggleBodyScroll(false);
        });
    }

    // ===== UTILITY FUNCTIONS =====
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // ===== INTERSECTION OBSERVER FOR ANIMATIONS =====
    function initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observe elements for animation
            $('.service-card, .value-item, .contact-item').each(function() {
                observer.observe(this);
            });
        }
    }

    // ===== PERFORMANCE OPTIMIZATIONS =====
    function initPerformanceOptimizations() {
        // Lazy load images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            $('img[data-src]').each(function() {
                imageObserver.observe(this);
            });
        }
    }

    // ===== ACCESSIBILITY ENHANCEMENTS =====
    function initAccessibility() {
        // Keyboard navigation for custom elements
        $('.btn, .nav-link').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).click();
            }
        });

        // Focus management for mobile menu
        $('.nav-toggle').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).click();
                $('.nav-menu .nav-link:first').focus();
            }
        });

        // Skip to content link
        $('body').prepend(`
            <a href="#main" class="skip-link sr-only" style="
                position: absolute;
                top: -40px;
                left: 6px;
                background: var(--accent-color);
                color: white;
                padding: 8px;
                text-decoration: none;
                z-index: 10000;
                transition: top 0.3s;
            ">Salta al contenuto principale</a>
        `);

        $('.skip-link').on('focus', function() {
            $(this).css('top', '6px').removeClass('sr-only');
        }).on('blur', function() {
            $(this).css('top', '-40px').addClass('sr-only');
        });
    }

    // ===== ERROR HANDLING =====
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
        // Could send error to analytics or logging service
    });

    // ===== INITIALIZE ADDITIONAL FEATURES =====
    $(document).ready(function() {
        // Initialize additional features after DOM is ready
        setTimeout(function() {
            initIntersectionObserver();
            initPerformanceOptimizations();
            initAccessibility();
        }, 100);
    });

})(jQuery);