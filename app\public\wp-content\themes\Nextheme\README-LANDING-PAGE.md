# Landing Page - Onoranze Funebri Ignagni Ceprano

## Descrizione
Landing page moderna ed elegante per le Onoranze Funebri Ignagni di Ceprano, realizzata con design responsive e animazioni fluide.

## File Creati/Modificati

### 1. front-page.php
Template principale della landing page con:
- Hero section con animazioni
- Sezione servizi con griglia responsive
- Sezione "Chi Siamo" con valori aziendali
- Sezione contatti con form e mappa
- Footer completo

### 2. assets/css/custom.css
Stili CSS moderni con:
- Variabili CSS per consistenza
- Design responsive
- Effetti glassmorphism
- Animazioni fluide
- Palette colori elegante (oro/bronzo su tonalità scure)

### 3. assets/js/main.js
JavaScript per interazioni e animazioni:
- Loading screen animato
- Navigazione sticky con trasparenza
- Scroll progress bar
- Validazione form in tempo reale
- Effetti parallax
- Back to top button
- <PERSON>ie banner
- Animazioni AOS

## Caratteristiche Principali

### Design
- **Palette Colori**: Tonalità scure (#1a1a1a, #2c2c2c) con accenti oro (#d4af37)
- **Tipografia**: Playfair Display per i titoli, Inter per il testo
- **Layout**: Responsive con breakpoint ottimizzati
- **Effetti**: Glassmorphism, gradients, ombre morbide

### Animazioni
- **Loading Screen**: Logo che appare gradualmente con spinner
- **Hero**: Fade-in progressivo degli elementi
- **Scroll**: Parallax leggero e progress bar
- **Cards**: Hover effects con trasformazioni 3D
- **Form**: Validazione interattiva con feedback visivo

### Sezioni

#### Hero Section
- Titolo impattante con gradient text
- Sottotitolo rassicurante
- Call-to-action buttons
- Features highlight (24/7, Discrezione, Esperienza)
- Scroll indicator animato

#### Servizi
- Griglia responsive 3x2
- Cards con hover effects
- Icone FontAwesome
- Descrizioni dettagliate
- Features list per ogni servizio

#### Chi Siamo
- Layout a due colonne
- Storia aziendale
- Valori con icone
- Immagine con overlay

#### Contatti
- Informazioni complete
- Form di contatto validato
- Mappa Google Maps
- Orari di apertura
- Reperibilità 24/7

### Funzionalità JavaScript

#### Navigazione
- Sticky navigation con backdrop-filter
- Active link highlighting
- Mobile menu hamburger
- Smooth scrolling

#### Form
- Validazione in tempo reale
- Formattazione numero telefono
- Messaggi di errore personalizzati
- Stato di loading durante invio

#### Effetti
- Parallax background
- Particle effects
- Scroll animations
- Intersection Observer per performance

## Personalizzazione

### Colori
Modifica le variabili CSS in `custom.css`:
```css
:root {
    --primary-color: #1a1a1a;
    --accent-color: #d4af37;
    /* ... altre variabili */
}
```

### Contenuti
Modifica direttamente in `front-page.php`:
- Testi e messaggi
- Informazioni di contatto
- Servizi offerti
- Storia aziendale

### Immagini
Sostituisci le immagini in `/assets/img/`:
- Logo aziendale
- Immagini di background
- Foto panoramiche

## Ottimizzazioni

### Performance
- CSS e JS minificati
- Immagini WebP
- Lazy loading
- Preload risorse critiche
- Defer script non critici

### SEO
- Meta tags ottimizzati
- Structured data
- Alt text per immagini
- Heading hierarchy corretta

### Accessibilità
- ARIA labels
- Contrasti conformi WCAG
- Navigazione da tastiera
- Screen reader friendly

## Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Dipendenze
- jQuery (incluso in WordPress)
- FontAwesome (per le icone)
- AOS (Animate On Scroll)
- Google Fonts (Playfair Display, Inter)

## Note Tecniche

### WordPress Integration
- Utilizza `get_template_directory_uri()` per i path
- Compatibile con WordPress 5.0+
- Enqueue corretto di CSS e JS tramite functions.php

### Mobile First
- Design responsive da mobile a desktop
- Touch-friendly interactions
- Ottimizzato per dispositivi touch

### Performance
- Critical CSS inline
- Non-blocking resource loading
- Optimized animations (60fps)
- Minimal DOM manipulation

## Manutenzione

### Aggiornamenti Contenuti
1. Modifica i testi in `front-page.php`
2. Aggiorna le informazioni di contatto
3. Sostituisci le immagini se necessario

### Monitoraggio
- Controlla la velocità di caricamento
- Verifica la compatibilità browser
- Testa su dispositivi mobili

## Supporto
Per assistenza tecnica o personalizzazioni aggiuntive, consultare la documentazione WordPress o contattare uno sviluppatore qualificato.
