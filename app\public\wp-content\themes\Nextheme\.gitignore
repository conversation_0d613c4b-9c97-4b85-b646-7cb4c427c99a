# Ignore OS files
.DS_Store
Thumbs.db

# Ignore editor files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Ignore node dependency directories
node_modules/

# Ignore log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore build files
/build/
/dist/

# Ignore composer dependencies
/vendor/

# Ignore environment files
.env
.env.*
!.env.example

# Ignore package lock files
package-lock.json
yarn.lock

# Ignore backup files
*.bak
*.swp
*~
