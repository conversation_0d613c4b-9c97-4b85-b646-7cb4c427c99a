<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package NextNaked
 */

/**
 * Adds custom classes to the array of body classes.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */
function nextnaked_body_classes( $classes ) {
	// Adds a class of hfeed to non-singular pages.
	if ( ! is_singular() ) {
		$classes[] = 'hfeed';
	}

	// Adds a class of no-sidebar when there is no sidebar present.
	if ( ! is_active_sidebar( 'sidebar-1' ) ) {
		$classes[] = 'no-sidebar';
	}

	// Add a class if it's a single post or page.
	if ( is_single() || is_page() ) {
		$classes[] = 'singular';
	}

	return $classes;
}
add_filter( 'body_class', 'nextnaked_body_classes' );

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function nextnaked_pingback_header() {
	if ( is_singular() && pings_open() ) {
		printf( '<link rel="pingback" href="%s">', esc_url( get_bloginfo( 'pingback_url' ) ) );
	}
}
add_action( 'wp_head', 'nextnaked_pingback_header' );

/**
 * Note: The functions to remove unnecessary meta tags have been removed
 * as they are considered plugin-territory functionality and should not be
 * included in themes according to WordPress.org guidelines.
 */

/**
 * Add async/defer attributes to enqueued scripts where needed.
 *
 * @param string $tag    The script tag.
 * @param string $handle The script handle.
 * @return string Script HTML string.
 */
function nextnaked_script_loader_tag( $tag, $handle ) {
	// The handles of the enqueued scripts we want to async
	$async_scripts = array( 'nextnaked-analytics' );

	if ( in_array( $handle, $async_scripts ) ) {
		return str_replace( ' src', ' async src', $tag );
	}

	return $tag;
}
add_filter( 'script_loader_tag', 'nextnaked_script_loader_tag', 10, 2 );

/**
 * Optimize images by adding loading="lazy" attribute to img tags
 *
 * @param string $content The content to be filtered.
 * @return string The filtered content.
 */
function nextnaked_add_lazy_loading( $content ) {
	// Don't lazy load if AMP is active
	if ( function_exists( 'is_amp_endpoint' ) && is_amp_endpoint() ) {
		return $content;
	}

	// Skip if content is empty
	if ( empty( $content ) ) {
		return $content;
	}

	// Replace img tags with lazy loading attribute
	$content = preg_replace( '/<img(.*?)>/i', '<img$1 loading="lazy">', $content );

	return $content;
}
add_filter( 'the_content', 'nextnaked_add_lazy_loading', 99 );
add_filter( 'post_thumbnail_html', 'nextnaked_add_lazy_loading', 99 );

/**
 * Add preconnect for Google Fonts.
 *
 * @param array  $urls           URLs to print for resource hints.
 * @param string $relation_type  The relation type the URLs are printed.
 * @return array $urls           URLs to print for resource hints.
 */
function nextnaked_resource_hints( $urls, $relation_type ) {
	if ( wp_style_is( 'nextnaked-fonts', 'queue' ) && 'preconnect' === $relation_type ) {
		$urls[] = array(
			'href' => 'https://fonts.gstatic.com',
			'crossorigin',
		);
	}

	return $urls;
}
add_filter( 'wp_resource_hints', 'nextnaked_resource_hints', 10, 2 );
