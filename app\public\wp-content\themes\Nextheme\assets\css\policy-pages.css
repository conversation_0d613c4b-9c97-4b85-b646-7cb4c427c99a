/* Policy Pages Styles (Privacy Policy & Cookie Policy) */

/* Common styles for both policy pages */
.policy-page, .privacy-policy, .cookie-page {
    padding: 0; /* <PERSON><PERSON>sso completamente il padding */
    background-color: #f9f9f9;
    color: #2B2D37;
}

.policy-page .containers,
.privacy-policy .containers,
.cookie-page .containers {
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
}

.policy-page h1,
.privacy-policy h1,
.cookie-page h1 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #2B2D37;
    text-align: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.policy-page h1 span,
.privacy-policy h1 span,
.cookie-page h1 span {
    color: #FABC3D;
    font-weight: 700;
}

.policy-page .section,
.privacy-policy .section,
.cookie-page .section {
    margin-bottom: 30px;
}

.policy-page .intro-section,
.privacy-policy .intro-section,
.cookie-page .important {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 40px;
}

.policy-page .last-update,
.privacy-policy .last-update,
.cookie-page .last-update {
    color: #777;
    font-size: 16px;
    font-style: italic;
    margin-bottom: 10px;
}

.policy-page h2,
.privacy-policy h2,
.cookie-page h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2B2D37;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.policy-page p,
.privacy-policy p,
.cookie-page p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.policy-page ul,
.privacy-policy ul,
.cookie-page ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.policy-page ul li,
.privacy-policy ul li,
.cookie-page ul li {
    margin-bottom: 8px;
    position: relative;
    padding-left: 15px;
    list-style-type: none;
}

.policy-page ul li:before,
.privacy-policy ul li:before,
.cookie-page ul li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 10px;
    width: 6px;
    height: 6px;
    background-color: #FABC3D;
    border-radius: 50%;
}

.policy-page .highlight,
.privacy-policy .highlight,
.cookie-page .important {
    background-color: #f9f9f9;
    padding: 25px;
    border-left: 4px solid #FABC3D;
    border-radius: 0 5px 5px 0;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.policy-page .contact-info,
.privacy-policy .contact-info,
.cookie-page .contact-info {
    margin-top: 20px;
}

.policy-page .contact-item,
.privacy-policy .contact-item,
.cookie-page .contact-item {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.policy-page .contact-label,
.privacy-policy .contact-label,
.cookie-page .contact-label {
    font-weight: 600;
    min-width: 80px;
    color: #2B2D37;
}

.policy-page .contact-value,
.privacy-policy .contact-value,
.cookie-page .contact-value {
    color: #555;
}

/* Cookie Policy specific styles */
.cookie-page a {
    color: #FABC3D;
    text-decoration: none;
    transition: all 0.3s ease;
}

.cookie-page a:hover {
    text-decoration: underline;
}

/* Breadcrumb styles */
.privacy-breadcrumb,
.cookie-breadcrumb {
    background-color: #2B2D37;
    padding: 20px 0 30px 0; /* Ridotto il padding-top e aumentato quello inferiore */
    margin-bottom: 0;
}

.privacy-breadcrumb .container,
.cookie-breadcrumb .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.privacy-breadcrumb h2,
.cookie-breadcrumb h2 {
    color: #fff;
    font-size: 32px;
    margin-bottom: 10px;
    font-weight: 600;
}

.privacy-breadcrumb .breadcrumb-list,
.cookie-breadcrumb .breadcrumb-list {
    display: flex;
    list-style: none;
}

.privacy-breadcrumb .breadcrumb-item,
.cookie-breadcrumb .breadcrumb-item {
    color: #FABC3D;
    font-size: 16px;
}

.privacy-breadcrumb .breadcrumb-item a,
.cookie-breadcrumb .breadcrumb-item a {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.privacy-breadcrumb .breadcrumb-item a:hover,
.cookie-breadcrumb .breadcrumb-item a:hover {
    color: #FABC3D;
}

.privacy-breadcrumb .breadcrumb-item + .breadcrumb-item:before,
.cookie-breadcrumb .breadcrumb-item + .breadcrumb-item:before {
    content: "/";
    padding: 0 10px;
    color: #fff;
}

/* Responsive styles */
/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
    .privacy-breadcrumb,
    .cookie-breadcrumb {
        padding: 15px 0 25px 0; /* Padding ottimizzato per tablet */
    }
}

/* Mobile */
@media (max-width: 768px) {
    .policy-page,
    .privacy-policy,
    .cookie-page {
        padding: 0; /* Rimosso completamente il padding */
    }

    .policy-page .containers,
    .privacy-policy .containers,
    .cookie-page .containers {
        padding: 20px;
    }

    .policy-page h1,
    .privacy-policy h1,
    .cookie-page h1 {
        font-size: 28px;
    }

    .policy-page h2,
    .privacy-policy h2,
    .cookie-page h2 {
        font-size: 20px;
    }

    .privacy-breadcrumb,
    .cookie-breadcrumb {
        padding: 10px 0 20px 0; /* Ridotto il padding-top e aumentato quello inferiore su mobile */
    }

    .privacy-breadcrumb h2,
    .cookie-breadcrumb h2 {
        font-size: 24px;
    }
}
