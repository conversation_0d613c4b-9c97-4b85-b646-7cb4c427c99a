/* Privacy Policy Page Styles */

.privacy-policy {
    padding: 80px 0;
    background-color: #f9f9f9;
    color: #2B2D37;
}

.privacy-policy .containers {
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
}

.privacy-policy h1 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #2B2D37;
    text-align: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.privacy-policy h1 span {
    color: #FABC3D;
    font-weight: 700;
}

.privacy-policy .section {
    margin-bottom: 30px;
}

.privacy-policy .intro-section {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 40px;
}

.privacy-policy .last-update {
    color: #777;
    font-size: 16px;
    font-style: italic;
    margin-bottom: 10px;
}

.privacy-policy h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2B2D37;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.privacy-policy p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.privacy-policy ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.privacy-policy ul li {
    margin-bottom: 8px;
    position: relative;
    padding-left: 15px;
    list-style-type: none;
}

.privacy-policy ul li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 10px;
    width: 6px;
    height: 6px;
    background-color: #FABC3D;
    border-radius: 50%;
}

.privacy-policy .highlight {
    background-color: #f9f9f9;
    padding: 25px;
    border-left: 4px solid #FABC3D;
    border-radius: 0 5px 5px 0;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.privacy-policy .contact-info {
    margin-top: 20px;
}

.privacy-policy .contact-item {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.privacy-policy .contact-label {
    font-weight: 600;
    min-width: 80px;
    color: #2B2D37;
}

.privacy-policy .contact-value {
    color: #555;
}

/* Breadcrumb styles */
.privacy-breadcrumb {
    background-color: #2B2D37;
    padding: 30px 0;
    margin-bottom: 0;
}

.privacy-breadcrumb .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.privacy-breadcrumb h2 {
    color: #fff;
    font-size: 32px;
    margin-bottom: 10px;
    font-weight: 600;
}

.privacy-breadcrumb .breadcrumb-list {
    display: flex;
    list-style: none;
}

.privacy-breadcrumb .breadcrumb-item {
    color: #FABC3D;
    font-size: 16px;
}

.privacy-breadcrumb .breadcrumb-item a {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.privacy-breadcrumb .breadcrumb-item a:hover {
    color: #FABC3D;
}

.privacy-breadcrumb .breadcrumb-item + .breadcrumb-item:before {
    content: "/";
    padding: 0 10px;
    color: #fff;
}

/* Responsive styles */
@media (max-width: 768px) {
    .privacy-policy {
        padding: 40px 0;
    }

    .privacy-policy .containers {
        padding: 20px;
    }

    .privacy-policy h1 {
        font-size: 28px;
    }

    .privacy-policy h2 {
        font-size: 20px;
    }

    .privacy-breadcrumb {
        padding: 20px 0;
    }

    .privacy-breadcrumb h2 {
        font-size: 24px;
    }
}
