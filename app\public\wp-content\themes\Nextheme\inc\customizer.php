<?php
/**
 * NextNaked Theme Customizer
 *
 * @package NextNaked
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function nextnaked_customize_register( $wp_customize ) {
	$wp_customize->get_setting( 'blogname' )->transport         = 'postMessage';
	$wp_customize->get_setting( 'blogdescription' )->transport  = 'postMessage';
	$wp_customize->get_setting( 'header_textcolor' )->transport = 'postMessage';

	if ( isset( $wp_customize->selective_refresh ) ) {
		$wp_customize->selective_refresh->add_partial(
			'blogname',
			array(
				'selector'        => '.site-title a',
				'render_callback' => 'nextnaked_customize_partial_blogname',
			)
		);
		$wp_customize->selective_refresh->add_partial(
			'blogdescription',
			array(
				'selector'        => '.site-description',
				'render_callback' => 'nextnaked_customize_partial_blogdescription',
			)
		);
	}
	
	// Add Theme Options Panel
	$wp_customize->add_panel(
		'nextnaked_theme_options',
		array(
			'title'       => esc_html__( 'Theme Options', 'nextnaked' ),
			'description' => esc_html__( 'Theme Options for NextNaked Theme', 'nextnaked' ),
			'priority'    => 130,
		)
	);
	
	// Add Layout Section
	$wp_customize->add_section(
		'nextnaked_layout_options',
		array(
			'title'       => esc_html__( 'Layout Options', 'nextnaked' ),
			'description' => esc_html__( 'Configure layout options', 'nextnaked' ),
			'panel'       => 'nextnaked_theme_options',
			'priority'    => 10,
		)
	);
	
	// Add Sidebar Position Setting
	$wp_customize->add_setting(
		'nextnaked_sidebar_position',
		array(
			'default'           => 'right',
			'sanitize_callback' => 'nextnaked_sanitize_select',
			'transport'         => 'refresh',
		)
	);
	
	$wp_customize->add_control(
		'nextnaked_sidebar_position',
		array(
			'label'       => esc_html__( 'Sidebar Position', 'nextnaked' ),
			'description' => esc_html__( 'Choose the position of the sidebar', 'nextnaked' ),
			'section'     => 'nextnaked_layout_options',
			'type'        => 'select',
			'choices'     => array(
				'left'  => esc_html__( 'Left', 'nextnaked' ),
				'right' => esc_html__( 'Right', 'nextnaked' ),
				'none'  => esc_html__( 'No Sidebar', 'nextnaked' ),
			),
		)
	);
	
	// Add Container Width Setting
	$wp_customize->add_setting(
		'nextnaked_container_width',
		array(
			'default'           => '1200',
			'sanitize_callback' => 'absint',
			'transport'         => 'postMessage',
		)
	);
	
	$wp_customize->add_control(
		'nextnaked_container_width',
		array(
			'label'       => esc_html__( 'Container Width (px)', 'nextnaked' ),
			'description' => esc_html__( 'Set the width of the main container', 'nextnaked' ),
			'section'     => 'nextnaked_layout_options',
			'type'        => 'number',
			'input_attrs' => array(
				'min'  => 960,
				'max'  => 1600,
				'step' => 10,
			),
		)
	);
	
	// Add Performance Section
	$wp_customize->add_section(
		'nextnaked_performance_options',
		array(
			'title'       => esc_html__( 'Performance Options', 'nextnaked' ),
			'description' => esc_html__( 'Configure performance options', 'nextnaked' ),
			'panel'       => 'nextnaked_theme_options',
			'priority'    => 20,
		)
	);
	
	// Add Lazy Loading Setting
	$wp_customize->add_setting(
		'nextnaked_enable_lazy_loading',
		array(
			'default'           => true,
			'sanitize_callback' => 'nextnaked_sanitize_checkbox',
		)
	);
	
	$wp_customize->add_control(
		'nextnaked_enable_lazy_loading',
		array(
			'label'       => esc_html__( 'Enable Lazy Loading', 'nextnaked' ),
			'description' => esc_html__( 'Lazy load images for better performance', 'nextnaked' ),
			'section'     => 'nextnaked_performance_options',
			'type'        => 'checkbox',
		)
	);
	
	// Add Disable Emojis Setting
	$wp_customize->add_setting(
		'nextnaked_disable_emojis',
		array(
			'default'           => true,
			'sanitize_callback' => 'nextnaked_sanitize_checkbox',
		)
	);
	
	$wp_customize->add_control(
		'nextnaked_disable_emojis',
		array(
			'label'       => esc_html__( 'Disable WordPress Emojis', 'nextnaked' ),
			'description' => esc_html__( 'Disable WordPress emoji scripts for better performance', 'nextnaked' ),
			'section'     => 'nextnaked_performance_options',
			'type'        => 'checkbox',
		)
	);
}
add_action( 'customize_register', 'nextnaked_customize_register' );

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function nextnaked_customize_partial_blogname() {
	bloginfo( 'name' );
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function nextnaked_customize_partial_blogdescription() {
	bloginfo( 'description' );
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function nextnaked_customize_preview_js() {
	wp_enqueue_script( 'nextnaked-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array( 'customize-preview' ), NEXTNAKED_VERSION, true );
}
add_action( 'customize_preview_init', 'nextnaked_customize_preview_js' );

/**
 * Sanitize select field
 *
 * @param string $input   The input from the setting.
 * @param object $setting The selected setting.
 *
 * @return string The sanitized input.
 */
function nextnaked_sanitize_select( $input, $setting ) {
	// Get list of choices from the control associated with the setting.
	$choices = $setting->manager->get_control( $setting->id )->choices;
	
	// If the input is a valid key, return it; otherwise, return the default.
	return ( array_key_exists( $input, $choices ) ? $input : $setting->default );
}

/**
 * Sanitize checkbox field
 *
 * @param bool $checked Whether the checkbox is checked.
 *
 * @return bool Whether the checkbox is checked.
 */
function nextnaked_sanitize_checkbox( $checked ) {
	return ( ( isset( $checked ) && true === $checked ) ? true : false );
}
