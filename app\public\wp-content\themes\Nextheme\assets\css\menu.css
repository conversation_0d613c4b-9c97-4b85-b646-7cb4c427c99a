/*
 * Menu Styles for Nextheme
 * Dedicated CSS file for all menu-related styling
 */

/* ===== HEADER VISIBILITY FIX ===== */
/* Ensure header is visible by default, override critical CSS */
.header-area,
.mobile-header {
    opacity: 1 !important;
}

/* Only apply fade-in effect when specifically needed */
.header-area.fade-in,
.mobile-header.fade-in {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.header-area.fade-in.visible,
.mobile-header.fade-in.visible {
    opacity: 1;
}

/* ===== OVERRIDE YELLOW THEME COLORS ===== */
/* Override all colors with Onoranze La Serenità "Terra Serena" Palette */

/* Background colors */
.background-color--default {
    background-color: #82413F !important; /* Bordeaux Terroso */
}

.background-color--default-overlay {
    background-color: rgba(130, 65, 63, 0.9) !important; /* Bordeaux Terroso overlay */
}

.background-color--default-light-overlay {
    background-color: rgba(130, 65, 63, 0.8) !important; /* Bordeaux Terroso light overlay */
}

/* Default button styles */
.default-btn {
    background: #834240 !important; /* Bordeaux per pulsanti CTA */
    color: #ffffff !important;
    border: 2px solid #834240 !important;
}

.default-btn:hover {
    background: transparent !important;
    color: #834240 !important;
    border: 2px solid #834240 !important;
}

/* Header and navigation overrides */
.header-top-info__link:hover,
.header-top-info__link span:hover {
    color: #82413F !important; /* Navy Profondo for hover states */
}

/* Submenu overrides */
.sub-menu {
    border-bottom: 3px solid #7A8B73 !important; /* Borgogna Soft accent */
}

.sub-menu li:hover > a {
    color: #82413F !important; /* Navy Profondo for hover states */
}

/* Search form button */
.header-search-form form button {
    background-color: #7A8B73 !important; /* Borgogna Soft for buttons */
}

/* Mobile menu overrides */
.offcanvas-navigation > ul > li > a:hover,
.offcanvas-navigation ul.sub-menu-mobile > li > a:hover {
    color: #82413F !important; /* Navy Profondo for hover states */
}

/* Contact widget overrides */
.off-canvas-contact-widget .header-contact-info__list li a:hover {
    color: #82413F !important; /* Navy Profondo for hover states */
}

/* Social icons overrides */
.off-canvas-widget-social a:hover {
    color: #82413F !important; /* Navy Profondo for hover states */
}

/* General link hover overrides */
a:hover {
    color: #82413F !important; /* Navy Profondo for hover states */
}

/* Selection color override */
::-moz-selection {
    background-color: #82413F !important; /* Navy Profondo for selection */
}

::selection {
    background-color: #82413F !important; /* Navy Profondo for selection */
}

/* ===== STICKY HEADER STYLES ===== */
/* Ensure sticky header has correct colors for both old and new menu */
.header-sticky.is-sticky .menu-bar-wrapper,
.header-sticky.is-sticky .menu-bar-wrapper-centered {
    background-color: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 2px 30px rgba(0, 0, 0, 0.15) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1001 !important; /* Higher z-index to stay above hero */
    width: 100% !important;
}

.header-sticky.is-sticky .default-btn,
.header-sticky.is-sticky .contact-btn-elegant {
    background: #834240 !important; /* Bordeaux per pulsanti CTA */
    color: #ffffff !important;
    border: 2px solid #834240 !important;
}

.header-sticky.is-sticky .default-btn:hover,
.header-sticky.is-sticky .contact-btn-elegant:hover {
    background: transparent !important;
    color: #834240 !important;
}

.header-sticky.is-sticky .main-nav-menu ul li a,
.header-sticky.is-sticky .menu-items-left li a,
.header-sticky.is-sticky .menu-items-right li a {
    color: #2D2D2D !important; /* Dark text on light backgrounds */
}

.header-sticky.is-sticky .main-nav-menu ul li a:hover,
.header-sticky.is-sticky .menu-items-left li a:hover,
.header-sticky.is-sticky .menu-items-right li a:hover {
    color: #82413F !important; /* Navy Profondo for hover states */
}

.header-sticky.is-sticky .main-nav-menu ul li.current-menu-item a,
.header-sticky.is-sticky .main-nav-menu ul li.current_page_item a,
.header-sticky.is-sticky .menu-items-left li.current-menu-item a,
.header-sticky.is-sticky .menu-items-right li.current-menu-item a {
    color: #82413F !important; /* Navy Profondo for active states */
}

/* ===== LANDING PAGE HEADER ADJUSTMENTS ===== */
/* Ensure header is properly positioned on landing page */
.landing-page .header-area {
    position: relative;
    z-index: 1000;
}

/* Make sure header content is visible on landing page */
.landing-page .header-area .menu-bar-wrapper {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Adjust header spacing for landing page */
.landing-page .header-area {
    margin-bottom: 0;
}

/* Ensure navigation links are visible */
.landing-page .main-nav-menu ul li a {
    color: #2D2D2D; /* Dark text on light backgrounds */
    font-weight: 500;
}

.landing-page .main-nav-menu ul li a:hover {
    color: #82413F; /* Navy Profondo for hover states */
}

/* Style the call button for landing page */
.landing-page .nav-call-button .default-btn {
    background-color: #834240; /* Bordeaux per pulsanti CTA */
    color: #fff;
    border: 2px solid #834240;
}

.landing-page .nav-call-button .default-btn:hover {
    background-color: transparent;
    color: #834240;
}

/* ===== LANDING PAGE LAYOUT ADJUSTMENTS ===== */
/* Hero section viewport constraints are handled in custom.css */
/* Ensure main content doesn't overlap with header */
.landing-page .main-content {
    position: relative;
    z-index: 1;
}

/* Smooth scroll offset for anchor links */
.landing-page section[id] {
    scroll-margin-top: 100px;
}

/* Mobile adjustments */
@media (max-width: 991px) {
    .landing-page section[id] {
        scroll-margin-top: 70px;
    }
}

/* ===== MENU INTEGRATION FOR LANDING PAGE ===== */
/* Style menu items for landing page navigation */
.landing-page .main-nav-menu ul li a[href^="#"] {
    position: relative;
}

/* Active state for anchor links */
.landing-page .main-nav-menu ul li a[href^="#"].active {
    color: #82413F; /* Navy Profondo for active states */
}

.landing-page .main-nav-menu ul li a[href^="#"].active::after {
    width: 100%;
}

/* Smooth scrolling for all anchor links */
html {
    scroll-behavior: smooth;
}

/* Header background transition on scroll */
.header-area.scrolled .menu-bar-wrapper {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 30px rgba(0, 0, 0, 0.15);
}

/* Logo adjustments for landing page */
.landing-page .brand-logo img {
    max-height: 40px;
    width: auto;
}

/* Ensure proper spacing in navigation */
.landing-page .navigation-area {
    gap: 20px;
}

/* ===== ELEGANT HEADER TOP DESIGN ===== */
/* Header top with contact information */
.header-top {
    background: linear-gradient(135deg, #82413F 0%, #34495E 100%); /* Navy Profondo gradient */
    color: #FFFFFF; /* White text on dark backgrounds */
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.header-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
    pointer-events: none;
}

.header-top-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.header-top-info.text-center {
    justify-content: center;
}

.header-top-info.text-end {
    justify-content: flex-end;
}

.header-top-info__image {
    display: flex;
    align-items: center;
    opacity: 0.9;
}

.header-top-info__image img {
    filter: brightness(0) saturate(100%) invert(100%) !important;
    -webkit-filter: brightness(0) saturate(100%) invert(100%) !important;
}

.header-top-info__text {
    color: #FFFFFF; /* White text on dark backgrounds */
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: color 0.3s ease;
}

.header-top-info__text:hover {
    color: #F7F4EA; /* Avorio chiaro for hover */
}

/* Make phone number clickable and styled */
.header-top-info a {
    color: #FFFFFF; /* White text on dark backgrounds */
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.header-top-info a:hover {
    color: #7A8B73; /* Borgogna Soft for hover accent */
    text-shadow: 0 0 5px rgba(142, 59, 70, 0.3);
}

/* Remove dark backgrounds from header area */
.header-area,
.mobile-header {
    background: none !important;
    background-color: transparent !important;
    background-image: none !important;
}

.header-area::before,
.mobile-header::before {
    display: none !important;
}

/* ===== HEADER POSITIONING FOR HERO SECTIONS ===== */
/* Make header absolute positioned so it doesn't push hero section down */
.header-area {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1001 !important;
    width: 100% !important;
}

.mobile-header {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1001 !important;
    width: 100% !important;
}

/* Remove any background images or patterns */
.bg-img,
.background-color--dark,
.background-repeat--x {
    background: none !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* ===== FULL WIDTH MENU CONTAINER ===== */
.menu-bar-full-width {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

/* ===== ELEGANT MAIN MENU DESIGN ===== */
.menu-bar-wrapper-centered {
    background-color: #ffffff;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 18px 0;
    border-bottom: 3px solid #82413F; /* Slate Blue border */
    width: 100%;
    position: relative;
}

.menu-bar-wrapper-centered::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #82413F 0%, #6B7A8A 50%, #82413F 100%); /* Slate Blue gradient */
}

.centered-menu-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Left Menu Section */
.menu-left {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding-right: 40px;
}

.menu-items-left {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 35px;
}

.menu-items-left li a {
    color: #343a40; /* Dark text on light backgrounds */
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.5px;
    padding: 8px 0;
    position: relative;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 14px;
}

.menu-items-left li a:hover {
    color: #82413F; /* Slate Blue for hover states */
}

.menu-items-left li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: #7A8B73; /* Sage Green accent */
    transition: width 0.3s ease;
}

.menu-items-left li a:hover::after,
.menu-items-left li.current-menu-item a::after {
    width: 100%;
}

.menu-items-left li.current-menu-item a {
    color: #82413F; /* Slate Blue for active states */
}

/* Center Logo Section */
.menu-center {
    flex: 0 0 auto;
    text-align: center;
}

.brand-logo-centered {
    padding: 10px 20px;
}

.brand-logo-centered img {
    max-height: 65px; /* Ingrandito da 50px */
    width: auto;
    transition: transform 0.3s ease;
}

.brand-logo-centered:hover img {
    transform: scale(1.05);
}

/* Right Menu Section */
.menu-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 40px;
    gap: 30px;
}

.menu-items-right {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 35px;
}

.menu-items-right li a {
    color: #343a40; /* Dark text on light backgrounds */
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.5px;
    padding: 8px 0;
    position: relative;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 14px;
}

.menu-items-right li a:hover {
    color: #82413F; /* Slate Blue for hover states */
}

.menu-items-right li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: #7A8B73; /* Sage Green accent */
    transition: width 0.3s ease;
}

.menu-items-right li a:hover::after,
.menu-items-right li.current-menu-item a::after {
    width: 100%;
}

.menu-items-right li.current-menu-item a {
    color: #82413F; /* Slate Blue for active states */
}

/* Elegant Contact Button */
.nav-call-button-centered {
    flex-shrink: 0;
}

.contact-btn-elegant {
    background: #834240; /* Bordeaux per pulsanti CTA */
    color: #ffffff;
    padding: 12px 25px;
    border-radius: 30px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: 2px solid #834240;
    box-shadow: 0 4px 15px rgba(131, 66, 64, 0.2);
}

.contact-btn-elegant:hover {
    background: transparent;
    color: #834240; /* Bordeaux per hover text */
    border-color: #834240;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(131, 66, 64, 0.3);
}

.contact-btn-elegant .btn-icon {
    font-size: 12px;
}

.contact-btn-elegant .btn-text {
    font-weight: 600;
}

/* ===== RESPONSIVE ELEGANT HEADER ===== */
@media (max-width: 1599px) {
    .centered-menu-layout {
        max-width: 1200px;
        padding: 0 40px;
    }
}

@media (max-width: 1399px) {
    .centered-menu-layout {
        max-width: 1100px;
        padding: 0 30px;
    }

    .header-top-info {
        font-size: 13px;
    }
}

@media (max-width: 1199px) {
    .centered-menu-layout {
        max-width: 1000px;
        padding: 0 20px;
    }

    .menu-left,
    .menu-right {
        padding-left: 15px;
        padding-right: 15px;
    }

    .menu-items-left,
    .menu-items-right {
        gap: 20px;
    }

    .menu-items-left li a,
    .menu-items-right li a {
        font-size: 13px;
    }

    .contact-btn-elegant {
        padding: 10px 18px;
        font-size: 13px;
    }

    .header-top {
        padding: 10px 0;
    }

    .header-top-info {
        font-size: 12px;
        gap: 6px;
    }

    .header-top-info__text {
        display: none; /* Hide labels on smaller screens */
    }
}

@media (max-width: 991px) {
    /* Hide desktop header on mobile */
    .header-area {
        display: none !important;
    }

    .mobile-header {
        display: block !important;
    }
}

/* ===== ELEGANT ANIMATIONS ===== */
@keyframes headerTopSlideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.header-top {
    animation: headerTopSlideDown 0.6s ease-out;
}

.header-top-info {
    animation: fadeInUp 0.8s ease-out;
}

.header-top-info:nth-child(1) { animation-delay: 0.1s; }
.header-top-info:nth-child(2) { animation-delay: 0.2s; }
.header-top-info:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== MOBILE MENU ADJUSTMENTS ===== */
@media (max-width: 991px) {
    .mobile-header {
        display: block !important;
    }

    .header-area {
        display: none;
    }
}

@media (min-width: 992px) {
    .mobile-header {
        display: none !important;
    }

    .header-area {
        display: block;
    }
}

/* ===== STICKY MENU ELEGANT STYLES ===== */
.header-sticky.is-sticky .header-top {
    display: none; /* Hide header top when sticky */
}

.header-sticky.is-sticky .menu-bar-full-width {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100vw;
    z-index: 1001; /* Higher z-index to stay above hero */
    margin-left: 0;
    margin-right: 0;
}

.header-sticky.is-sticky .menu-bar-wrapper-centered {
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow: 0 4px 30px rgba(44, 62, 80, 0.2);
    padding: 10px 0;
    width: 100%;
    border-bottom: 2px solid #82413F; /* Slate Blue */
}

.header-sticky.is-sticky .menu-bar-wrapper-centered::before {
    background: linear-gradient(90deg, #82413F 0%, #6B7A8A 50%, #82413F 100%); /* Slate Blue gradient */
    height: 1px;
}

.header-sticky.is-sticky .brand-logo-centered img {
    max-height: 35px;
}

.header-sticky.is-sticky .contact-btn-elegant {
    padding: 8px 18px;
    font-size: 12px;
}

.header-sticky.is-sticky .centered-menu-layout {
    max-width: 1400px;
    padding: 0 40px;
}

.header-sticky.is-sticky .menu-items-left li a,
.header-sticky.is-sticky .menu-items-right li a {
    font-size: 13px;
}

/* No body padding needed - menu always overlays content */
/* Hero sections are full height and menu overlays on top */

/* Ensure all menu elements appear above all content */
.header-sticky.is-sticky .menu-bar-wrapper,
.header-sticky.is-sticky .menu-bar-wrapper-centered,
.header-area,
.mobile-header {
    z-index: 1001 !important; /* Higher than hero section z-index */
}

/* Ensure full width on sticky */
@media (max-width: 1599px) {
    .header-sticky.is-sticky .centered-menu-layout {
        max-width: 1200px;
    }
}

@media (max-width: 1399px) {
    .header-sticky.is-sticky .centered-menu-layout {
        max-width: 1100px;
        padding: 0 30px;
    }
}

@media (max-width: 1199px) {
    .header-sticky.is-sticky .centered-menu-layout {
        max-width: 1000px;
        padding: 0 20px;
    }
}

/* ===== ELEGANT MENU ANIMATIONS ===== */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-bar-wrapper-centered {
    animation: fadeInDown 0.6s ease-out;
}

.menu-items-left li,
.menu-items-right li {
    animation: fadeInDown 0.6s ease-out;
}

.menu-items-left li:nth-child(1) { animation-delay: 0.1s; }
.menu-items-left li:nth-child(2) { animation-delay: 0.2s; }
.menu-items-right li:nth-child(1) { animation-delay: 0.3s; }
.menu-items-right li:nth-child(2) { animation-delay: 0.4s; }

.brand-logo-centered {
    animation: fadeInDown 0.6s ease-out 0.2s both;
}

/* Logo sizing responsive */
/* Desktop and large screens */
@media (min-width: 1200px) {
    .brand-logo-centered img {
        max-height: 75px; /* Più grande su desktop */
    }
}

/* Laptop screens */
@media (min-width: 992px) and (max-width: 1199px) {
    .brand-logo-centered img {
        max-height: 70px; /* Dimensione per laptop */
    }
}

/* Tablet screens */
@media (max-width: 991px) {
    .brand-logo-centered img {
        max-height: 60px; /* Dimensione per tablet */
    }
}

/* Mobile screens */
@media (max-width: 767px) {
    .brand-logo-centered img {
        max-height: 50px; /* Dimensione per mobile */
    }
}

.contact-btn-elegant {
    animation: fadeInDown 0.6s ease-out 0.5s both;
}

/* ===== ELEGANT HOVER EFFECTS ===== */
.menu-items-left li,
.menu-items-right li {
    transition: transform 0.3s ease;
}

.menu-items-left li:hover,
.menu-items-right li:hover {
    transform: translateY(-2px);
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.menu-items-left li a:focus,
.menu-items-right li a:focus,
.contact-btn-elegant:focus {
    outline: 2px solid #82413F; /* Slate Blue */
    outline-offset: 3px;
    border-radius: 3px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .menu-bar-wrapper-centered {
        display: none;
    }
}

/* Dropdown menu styles */
.main-nav-menu ul li.menu-item-has-children > a::before {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 8px;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.main-nav-menu ul li.menu-item-has-children:hover > a::before {
    transform: rotate(180deg);
}

.main-nav-menu ul li ul.sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #fff;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 999;
    flex-direction: column;
    padding: 10px 0;
}

.main-nav-menu ul li:hover ul.sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.main-nav-menu ul li ul.sub-menu li {
    margin: 0;
    width: 100%;
}

.main-nav-menu ul li ul.sub-menu li a {
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.main-nav-menu ul li ul.sub-menu li:last-child a {
    border-bottom: none;
}

.main-nav-menu ul li ul.sub-menu li a:hover {
    background-color: #f8f9fa;
    padding-left: 25px;
}

/* Call now button */
.nav-call-button {
    margin-left: 30px;
}

.nav-call-button .default-btn {
    background-color: #834240; /* Bordeaux per pulsanti CTA */
    color: #fff;
    padding: 12px 20px;
    border-radius: 5px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid #834240;
}

.nav-call-button .default-btn:hover {
    background-color: transparent;
    color: #834240;
}

.nav-call-button .call-icon {
    margin-right: 8px;
    display: flex;
    align-items: center;
}

.nav-call-button .call-icon img {
    filter: brightness(0) saturate(100%) invert(100%);
    transition: filter 0.3s ease;
}

.nav-call-button .default-btn:hover .call-icon img {
    filter: brightness(0) saturate(100%) invert(21%) sepia(19%) saturate(1230%) hue-rotate(184deg) brightness(94%) contrast(89%);
}

/* ===== ELEGANT MOBILE HEADER ===== */
.mobile-header {
    background: linear-gradient(135deg, #82413F 0%, #6B7A8A 100%) !important; /* Slate Blue gradient */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    padding: 15px 0 !important;
    border-bottom: 3px solid #34495e !important;
}

.mobile-header .brand-logo img {
    filter: brightness(0) saturate(100%) invert(100%) !important;
    -webkit-filter: brightness(0) saturate(100%) invert(100%) !important;
    max-height: 40px;
    drop-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* ===== MOBILE MENU TRIGGER ===== */
.mobile-menu-trigger-wrapper {
    cursor: pointer;
    padding: 10px;
}

.mobile-menu-trigger {
    display: block;
    width: 30px;
    height: 3px;
    background-color: #ffffff;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mobile-menu-trigger::before,
.mobile-menu-trigger::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 3px;
    background-color: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mobile-menu-trigger::before {
    top: -8px;
}

.mobile-menu-trigger::after {
    bottom: -8px;
}

/* Mobile menu trigger animation when active */
.mobile-menu-trigger.active {
    background-color: transparent;
}

.mobile-menu-trigger.active::before {
    top: 0;
    transform: rotate(45deg);
}

.mobile-menu-trigger.active::after {
    bottom: 0;
    transform: rotate(-45deg);
}

/* ===== OFFCANVAS MOBILE MENU ===== */
.offcanvas-mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background-color: #fff;
    z-index: 9999;
    transition: right 0.3s ease;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.offcanvas-mobile-menu.active {
    right: 0;
}

.offcanvas-menu-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 30px;
    height: 30px;
    cursor: pointer;
    z-index: 10000;
}

.menu-close {
    display: block;
    width: 20px;
    height: 2px;
    background-color: #333;
    position: relative;
    transform: rotate(45deg);
}

.menu-close::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: #333;
    transform: rotate(-90deg);
}

.offcanvas-wrapper {
    padding: 60px 30px 30px;
    height: 100%;
}

.offcanvas-inner-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Mobile call button */
.offcanvas-mobile-call-button {
    margin-bottom: 30px;
}

.offcanvas-mobile-call-button .default-btn {
    background-color: #834240; /* Bordeaux per pulsanti CTA */
    color: #fff;
    padding: 15px 20px;
    border-radius: 5px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    width: 100%;
    justify-content: center;
    transition: all 0.3s ease;
    border: 2px solid #834240;
}

.offcanvas-mobile-call-button .default-btn:hover {
    background-color: transparent;
    color: #834240;
}

/* Mobile navigation */
.offcanvas-navigation ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.offcanvas-navigation ul li {
    border-bottom: 1px solid #f0f0f0;
}

.offcanvas-navigation ul li:last-child {
    border-bottom: none;
}

.offcanvas-navigation ul li a {
    display: block;
    padding: 15px 0;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.3s ease;
}

.offcanvas-navigation ul li a:hover {
    color: #82413F; /* Slate Blue for hover states */
    padding-left: 10px;
}

/* Mobile submenu */
.offcanvas-navigation ul li.menu-item-has-children > a::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    float: right;
    transition: transform 0.3s ease;
}

.offcanvas-navigation ul li.menu-item-has-children.active > a::after {
    transform: rotate(180deg);
}

.offcanvas-navigation ul li ul.sub-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: #f8f9fa;
    margin-left: 20px;
    border-radius: 5px;
}

.offcanvas-navigation ul li.menu-item-has-children.active ul.sub-menu {
    max-height: 500px;
    padding: 10px 0;
}

.offcanvas-navigation ul li ul.sub-menu li a {
    padding: 10px 15px;
    font-size: 14px;
}

/* Mobile menu overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Contact widget in mobile menu */
.offcanvas-widget-area {
    margin-top: auto;
    padding-top: 30px;
    border-top: 1px solid #f0f0f0;
}

.header-contact-info__list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.header-contact-info__list li {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    font-size: 14px;
    color: #666;
}

.header-contact-info__list li:last-child {
    margin-bottom: 0;
}

.header-contact-info__list li .header-top-info__image {
    margin-right: 10px;
    flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .offcanvas-mobile-menu {
        width: 280px;
    }
    
    .offcanvas-wrapper {
        padding: 50px 20px 20px;
    }
}

@media (max-width: 480px) {
    .offcanvas-mobile-menu {
        width: 100%;
        right: -100%;
    }
}

/* Body scroll lock when mobile menu is open */
body.mobile-menu-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
}

/* Additional mobile menu animations */
.mobile-menu-trigger {
    cursor: pointer;
}

.mobile-menu-trigger,
.mobile-menu-trigger::before,
.mobile-menu-trigger::after {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* Smooth transitions for offcanvas menu */
.offcanvas-mobile-menu {
    transition: right 0.4s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.offcanvas-mobile-menu.active {
    right: 0;
}

/* Enhanced submenu animations */
.offcanvas-navigation ul li ul.sub-menu {
    transition: max-height 0.4s ease, padding 0.4s ease;
}

.offcanvas-navigation ul li.menu-item-has-children.active ul.sub-menu {
    max-height: 500px;
    padding: 10px 0;
}

/* Focus styles for accessibility */
.main-nav-menu ul li a:focus,
.nav-call-button .default-btn:focus,
.mobile-menu-trigger-wrapper:focus,
.offcanvas-navigation ul li a:focus {
    outline: 2px solid #82413F; /* Slate Blue */
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .main-nav-menu ul li a {
        border: 1px solid transparent;
    }

    .main-nav-menu ul li a:hover,
    .main-nav-menu ul li a:focus {
        border-color: currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .main-nav-menu ul li a::after,
    .mobile-menu-trigger,
    .mobile-menu-trigger::before,
    .mobile-menu-trigger::after,
    .offcanvas-mobile-menu,
    .offcanvas-navigation ul li ul.sub-menu {
        transition: none;
    }
}
