!function(y){"use strict";y.fn.counterUp=function(t){var g,m=y.extend({time:400,delay:10,offset:100,beginAt:0,formatter:!1,context:"window",callback:function(){}},t);return this.each(function(){var p=y(this),f={time:y(this).data("counterup-time")||m.time,delay:y(this).data("counterup-delay")||m.delay,offset:y(this).data("counterup-offset")||m.offset,beginAt:y(this).data("counterup-beginat")||m.beginAt,context:y(this).data("counterup-context")||m.context};p.waypoint(function(t){!function(){var t=[],e=f.time/f.delay,i=y(this).attr("data-num")?y(this).attr("data-num"):p.text(),s=/[0-9]+,[0-9]+/.test(i),o=((i=i.replace(/,/g,"")).split(".")[1]||[]).length;f.beginAt>i&&(f.beginAt=i);var n=/[0-9]+:[0-9]+:[0-9]+/.test(i);if(n){var r=i.split(":"),a=1;for(g=0;0<r.length;)g+=a*parseInt(r.pop(),10),a*=60}for(var l=e;l>=f.beginAt/i*e;l--){var d=parseFloat(i/e*l).toFixed(o);if(n){d=parseInt(g/e*l);var h=parseInt(d/3600)%24,c=parseInt(d/60)%60,u=parseInt(d%60,10);d=(h<10?"0"+h:h)+":"+(c<10?"0"+c:c)+":"+(u<10?"0"+u:u)}if(s)for(;/(\d+)(\d{3})/.test(d.toString());)d=d.toString().replace(/(\d+)(\d{3})/,"$1,$2");m.formatter&&(d=m.formatter.call(this,d)),t.unshift(d)}p.data("counterup-nums",t),p.text(f.beginAt);p.data("counterup-func",function(){p.data("counterup-nums")?(p.html(p.data("counterup-nums").shift()),p.data("counterup-nums").length?setTimeout(p.data("counterup-func"),f.delay):(p.data("counterup-nums",null),p.data("counterup-func",null),m.callback.call(this))):m.callback.call(this)}),setTimeout(p.data("counterup-func"),f.delay)}(),this.destroy()},{offset:f.offset+"%",context:f.context})})}}(jQuery),function(t,e){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",e):"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()}("undefined"!=typeof window?window:this,function(){function t(){}var e=t.prototype;return e.on=function(t,e){if(t&&e){var i=this._events=this._events||{},s=i[t]=i[t]||[];return-1==s.indexOf(e)&&s.push(e),this}},e.once=function(t,e){if(t&&e){this.on(t,e);var i=this._onceEvents=this._onceEvents||{};return(i[t]=i[t]||{})[e]=!0,this}},e.off=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){var s=i.indexOf(e);return-1!=s&&i.splice(s,1),this}},e.emitEvent=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){i=i.slice(0),e=e||[];for(var s=this._onceEvents&&this._onceEvents[t],o=0;o<i.length;o++){var n=i[o];s&&s[n]&&(this.off(t,n),delete s[n]),n.apply(this,e)}return this}},e.allOff=function(){delete this._events,delete this._onceEvents},t}),function(e,i){"use strict";"function"==typeof define&&define.amd?define(["ev-emitter/ev-emitter"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("ev-emitter")):e.imagesLoaded=i(e,e.EvEmitter)}("undefined"!=typeof window?window:this,function(e,t){function o(t,e){for(var i in e)t[i]=e[i];return t}function n(t,e,i){if(!(this instanceof n))return new n(t,e,i);var s=t;return"string"==typeof t&&(s=document.querySelectorAll(t)),s?(this.elements=function(t){return Array.isArray(t)?t:"object"==typeof t&&"number"==typeof t.length?l.call(t):[t]}(s),this.options=o({},this.options),"function"==typeof e?i=e:o(this.options,e),i&&this.on("always",i),this.getImages(),r&&(this.jqDeferred=new r.Deferred),void setTimeout(this.check.bind(this))):void a.error("Bad element for imagesLoaded "+(s||t))}function i(t){this.img=t}function s(t,e){this.url=t,this.element=e,this.img=new Image}var r=e.jQuery,a=e.console,l=Array.prototype.slice;(n.prototype=Object.create(t.prototype)).options={},n.prototype.getImages=function(){this.images=[],this.elements.forEach(this.addElementImages,this)},n.prototype.addElementImages=function(t){"IMG"==t.nodeName&&this.addImage(t),!0===this.options.background&&this.addElementBackgroundImages(t);var e=t.nodeType;if(e&&d[e]){for(var i=t.querySelectorAll("img"),s=0;s<i.length;s++){var o=i[s];this.addImage(o)}if("string"==typeof this.options.background){var n=t.querySelectorAll(this.options.background);for(s=0;s<n.length;s++){var r=n[s];this.addElementBackgroundImages(r)}}}};var d={1:!0,9:!0,11:!0};return n.prototype.addElementBackgroundImages=function(t){var e=getComputedStyle(t);if(e)for(var i=/url\((['"])?(.*?)\1\)/gi,s=i.exec(e.backgroundImage);null!==s;){var o=s&&s[2];o&&this.addBackground(o,t),s=i.exec(e.backgroundImage)}},n.prototype.addImage=function(t){var e=new i(t);this.images.push(e)},n.prototype.addBackground=function(t,e){var i=new s(t,e);this.images.push(i)},n.prototype.check=function(){function e(t,e,i){setTimeout(function(){s.progress(t,e,i)})}var s=this;return this.progressedCount=0,this.hasAnyBroken=!1,this.images.length?void this.images.forEach(function(t){t.once("progress",e),t.check()}):void this.complete()},n.prototype.progress=function(t,e,i){this.progressedCount++,this.hasAnyBroken=this.hasAnyBroken||!t.isLoaded,this.emitEvent("progress",[this,t,e]),this.jqDeferred&&this.jqDeferred.notify&&this.jqDeferred.notify(this,t),this.progressedCount==this.images.length&&this.complete(),this.options.debug&&a&&a.log("progress: "+i,t,e)},n.prototype.complete=function(){var t=this.hasAnyBroken?"fail":"done";if(this.isComplete=!0,this.emitEvent(t,[this]),this.emitEvent("always",[this]),this.jqDeferred){var e=this.hasAnyBroken?"reject":"resolve";this.jqDeferred[e](this)}},(i.prototype=Object.create(t.prototype)).check=function(){return this.getIsImageComplete()?void this.confirm(0!==this.img.naturalWidth,"naturalWidth"):(this.proxyImage=new Image,this.proxyImage.addEventListener("load",this),this.proxyImage.addEventListener("error",this),this.img.addEventListener("load",this),this.img.addEventListener("error",this),void(this.proxyImage.src=this.img.src))},i.prototype.getIsImageComplete=function(){return this.img.complete&&this.img.naturalWidth},i.prototype.confirm=function(t,e){this.isLoaded=t,this.emitEvent("progress",[this,this.img,e])},i.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},i.prototype.onload=function(){this.confirm(!0,"onload"),this.unbindEvents()},i.prototype.onerror=function(){this.confirm(!1,"onerror"),this.unbindEvents()},i.prototype.unbindEvents=function(){this.proxyImage.removeEventListener("load",this),this.proxyImage.removeEventListener("error",this),this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},(s.prototype=Object.create(i.prototype)).check=function(){this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.img.src=this.url,this.getIsImageComplete()&&(this.confirm(0!==this.img.naturalWidth,"naturalWidth"),this.unbindEvents())},s.prototype.unbindEvents=function(){this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},s.prototype.confirm=function(t,e){this.isLoaded=t,this.emitEvent("progress",[this,this.element,e])},n.makeJQueryPlugin=function(t){(t=t||e.jQuery)&&((r=t).fn.imagesLoaded=function(t,e){return new n(this,t,e).jqDeferred.promise(r(this))})},n.makeJQueryPlugin(),n}),function(i){"function"==typeof define&&define.amd?define(["jquery"],i):"object"==typeof module&&module.exports?module.exports=function(t,e){return void 0===e&&(e="undefined"!=typeof window?require("jquery"):require("jquery")(t)),i(e),e}:i(jQuery)}(function(h){function n(t,e){this.settings=e,this.checkSettings(),this.imgAnalyzerTimeout=null,this.entries=null,this.buildingRow={entriesBuff:[],width:0,height:0,aspectRatio:0},this.lastFetchedEntry=null,this.lastAnalyzedIndex=-1,this.yield={every:2,flushed:0},this.border=0<=e.border?e.border:e.margins,this.maxRowHeight=this.retrieveMaxRowHeight(),this.suffixRanges=this.retrieveSuffixRanges(),this.offY=this.border,this.rows=0,this.spinner={phase:0,timeSlot:150,$el:h('<div class="spinner"><span></span><span></span><span></span></div>'),intervalId:null},this.scrollBarOn=!1,this.checkWidthIntervalId=null,this.galleryWidth=t.width(),this.$gallery=t}n.prototype.getSuffix=function(t,e){var i,s;for(i=e<t?t:e,s=0;s<this.suffixRanges.length;s++)if(i<=this.suffixRanges[s])return this.settings.sizeRangeSuffixes[this.suffixRanges[s]];return this.settings.sizeRangeSuffixes[this.suffixRanges[s-1]]},n.prototype.removeSuffix=function(t,e){return t.substring(0,t.length-e.length)},n.prototype.endsWith=function(t,e){return-1!==t.indexOf(e,t.length-e.length)},n.prototype.getUsedSuffix=function(t){for(var e in this.settings.sizeRangeSuffixes)if(this.settings.sizeRangeSuffixes.hasOwnProperty(e)){if(0===this.settings.sizeRangeSuffixes[e].length)continue;if(this.endsWith(t,this.settings.sizeRangeSuffixes[e]))return this.settings.sizeRangeSuffixes[e]}return""},n.prototype.newSrc=function(t,e,i,s){var o;if(this.settings.thumbnailPath)o=this.settings.thumbnailPath(t,e,i,s);else{var n=t.match(this.settings.extension),r=null!==n?n[0]:"";o=t.replace(this.settings.extension,""),o=this.removeSuffix(o,this.getUsedSuffix(o)),o+=this.getSuffix(e,i)+r}return o},n.prototype.showImg=function(t,e){this.settings.cssAnimation?(t.addClass("jg-entry-visible"),e&&e()):(t.stop().fadeTo(this.settings.imagesAnimationDuration,1,e),t.find(this.settings.imgSelector).stop().fadeTo(this.settings.imagesAnimationDuration,1,e))},n.prototype.extractImgSrcFromImage=function(t){var e=t.data("safe-src"),i="date-safe-src";return void 0===e&&(e=t.attr("src"),i="src"),t.data("jg.originalSrc",e),t.data("jg.src",e),t.data("jg.originalSrcLoc",i),e},n.prototype.imgFromEntry=function(t){var e=t.find(this.settings.imgSelector);return 0===e.length?null:e},n.prototype.captionFromEntry=function(t){var e=t.find("> .caption");return 0===e.length?null:e},n.prototype.displayEntry=function(t,e,i,s,o,n){t.width(s),t.height(n),t.css("top",i),t.css("left",e);var r=this.imgFromEntry(t);if(null!==r){r.css("width",s),r.css("height",o),r.css("margin-left",-s/2),r.css("margin-top",-o/2);var a=r.data("jg.src");if(a){a=this.newSrc(a,s,o,r[0]),r.one("error",function(){this.resetImgSrc(r)});function l(){r.attr("src",a)}"skipped"===t.data("jg.loaded")?this.onImageEvent(a,function(){this.showImg(t,l),t.data("jg.loaded",!0)}.bind(this)):this.showImg(t,l)}}else this.showImg(t);this.displayEntryCaption(t)},n.prototype.displayEntryCaption=function(t){var e=this.imgFromEntry(t);if(null!==e&&this.settings.captions){var i=this.captionFromEntry(t);if(null===i){var s=e.attr("alt");this.isValidCaption(s)||(s=t.attr("title")),this.isValidCaption(s)&&(i=h('<div class="caption">'+s+"</div>"),t.append(i),t.data("jg.createdCaption",!0))}null!==i&&(this.settings.cssAnimation||i.stop().fadeTo(0,this.settings.captionSettings.nonVisibleOpacity),this.addCaptionEventsHandlers(t))}else this.removeCaptionEventsHandlers(t)},n.prototype.isValidCaption=function(t){return void 0!==t&&0<t.length},n.prototype.onEntryMouseEnterForCaption=function(t){var e=this.captionFromEntry(h(t.currentTarget));this.settings.cssAnimation?e.addClass("caption-visible").removeClass("caption-hidden"):e.stop().fadeTo(this.settings.captionSettings.animationDuration,this.settings.captionSettings.visibleOpacity)},n.prototype.onEntryMouseLeaveForCaption=function(t){var e=this.captionFromEntry(h(t.currentTarget));this.settings.cssAnimation?e.removeClass("caption-visible").removeClass("caption-hidden"):e.stop().fadeTo(this.settings.captionSettings.animationDuration,this.settings.captionSettings.nonVisibleOpacity)},n.prototype.addCaptionEventsHandlers=function(t){var e=t.data("jg.captionMouseEvents");void 0===e&&(e={mouseenter:h.proxy(this.onEntryMouseEnterForCaption,this),mouseleave:h.proxy(this.onEntryMouseLeaveForCaption,this)},t.on("mouseenter",void 0,void 0,e.mouseenter),t.on("mouseleave",void 0,void 0,e.mouseleave),t.data("jg.captionMouseEvents",e))},n.prototype.removeCaptionEventsHandlers=function(t){var e=t.data("jg.captionMouseEvents");void 0!==e&&(t.off("mouseenter",void 0,e.mouseenter),t.off("mouseleave",void 0,e.mouseleave),t.removeData("jg.captionMouseEvents"))},n.prototype.clearBuildingRow=function(){this.buildingRow.entriesBuff=[],this.buildingRow.aspectRatio=0,this.buildingRow.width=0},n.prototype.prepareBuildingRow=function(t){var e,i,s,o,n,r=!0,a=0,l=this.galleryWidth-2*this.border-(this.buildingRow.entriesBuff.length-1)*this.settings.margins,d=l/this.buildingRow.aspectRatio,h=this.settings.rowHeight,c=this.buildingRow.width/l>this.settings.justifyThreshold;if(t&&"hide"===this.settings.lastRow&&!c){for(e=0;e<this.buildingRow.entriesBuff.length;e++)i=this.buildingRow.entriesBuff[e],this.settings.cssAnimation?i.removeClass("jg-entry-visible"):(i.stop().fadeTo(0,.1),i.find("> img, > a > img").fadeTo(0,0));return-1}for(t&&!c&&"justify"!==this.settings.lastRow&&"hide"!==this.settings.lastRow&&(r=!1,0<this.rows&&(r=(h=(this.offY-this.border-this.settings.margins*this.rows)/this.rows)*this.buildingRow.aspectRatio/l>this.settings.justifyThreshold)),e=0;e<this.buildingRow.entriesBuff.length;e++)s=(i=this.buildingRow.entriesBuff[e]).data("jg.width")/i.data("jg.height"),n=r?(o=e===this.buildingRow.entriesBuff.length-1?l:d*s,d):(o=h*s,h),l-=Math.round(o),i.data("jg.jwidth",Math.round(o)),i.data("jg.jheight",Math.ceil(n)),(0===e||n<a)&&(a=n);return this.buildingRow.height=a,r},n.prototype.flushRow=function(t){var e,i,s,o=this.settings,n=this.border;if(i=this.prepareBuildingRow(t),t&&"hide"===o.lastRow&&-1===i)this.clearBuildingRow();else{if(this.maxRowHeight&&this.maxRowHeight<this.buildingRow.height&&(this.buildingRow.height=this.maxRowHeight),t&&("center"===o.lastRow||"right"===o.lastRow)){var r=this.galleryWidth-2*this.border-(this.buildingRow.entriesBuff.length-1)*o.margins;for(s=0;s<this.buildingRow.entriesBuff.length;s++)r-=(e=this.buildingRow.entriesBuff[s]).data("jg.jwidth");"center"===o.lastRow?n+=r/2:"right"===o.lastRow&&(n+=r)}var a=this.buildingRow.entriesBuff.length-1;for(s=0;s<=a;s++)e=this.buildingRow.entriesBuff[this.settings.rtl?a-s:s],this.displayEntry(e,n,this.offY,e.data("jg.jwidth"),e.data("jg.jheight"),this.buildingRow.height),n+=e.data("jg.jwidth")+o.margins;this.galleryHeightToSet=this.offY+this.buildingRow.height+this.border,this.setGalleryTempHeight(this.galleryHeightToSet+this.getSpinnerHeight()),(!t||this.buildingRow.height<=o.rowHeight&&i)&&(this.offY+=this.buildingRow.height+o.margins,this.rows+=1,this.clearBuildingRow(),this.settings.triggerEvent.call(this,"jg.rowflush"))}};var e=0;n.prototype.rememberGalleryHeight=function(){e=this.$gallery.height(),this.$gallery.height(e)},n.prototype.setGalleryTempHeight=function(t){e=Math.max(t,e),this.$gallery.height(e)},n.prototype.setGalleryFinalHeight=function(t){e=t,this.$gallery.height(t)},n.prototype.checkWidth=function(){this.checkWidthIntervalId=setInterval(h.proxy(function(){if(this.$gallery.is(":visible")){var t=parseFloat(this.$gallery.width());Math.abs(t-this.galleryWidth)>this.settings.refreshSensitivity&&(this.galleryWidth=t,this.rewind(),this.rememberGalleryHeight(),this.startImgAnalyzer(!0))}},this),this.settings.refreshTime)},n.prototype.isSpinnerActive=function(){return null!==this.spinner.intervalId},n.prototype.getSpinnerHeight=function(){return this.spinner.$el.innerHeight()},n.prototype.stopLoadingSpinnerAnimation=function(){clearInterval(this.spinner.intervalId),this.spinner.intervalId=null,this.setGalleryTempHeight(this.$gallery.height()-this.getSpinnerHeight()),this.spinner.$el.detach()},n.prototype.startLoadingSpinnerAnimation=function(){var t=this.spinner,e=t.$el.find("span");clearInterval(t.intervalId),this.$gallery.append(t.$el),this.setGalleryTempHeight(this.offY+this.buildingRow.height+this.getSpinnerHeight()),t.intervalId=setInterval(function(){t.phase<e.length?e.eq(t.phase).fadeTo(t.timeSlot,1):e.eq(t.phase-e.length).fadeTo(t.timeSlot,0),t.phase=(t.phase+1)%(2*e.length)},t.timeSlot)},n.prototype.rewind=function(){this.lastFetchedEntry=null,this.lastAnalyzedIndex=-1,this.offY=this.border,this.rows=0,this.clearBuildingRow()},n.prototype.getAllEntries=function(){return this.$gallery.children(this.settings.selector).toArray()},n.prototype.updateEntries=function(t){var e;return 0<(e=t&&null!=this.lastFetchedEntry?h(this.lastFetchedEntry).nextAll(this.settings.selector).toArray():(this.entries=[],this.getAllEntries())).length&&(h.isFunction(this.settings.sort)?e=this.sortArray(e):this.settings.randomize&&(e=this.shuffleArray(e)),this.lastFetchedEntry=e[e.length-1],this.settings.filter?e=this.filterArray(e):this.resetFilters(e)),this.entries=this.entries.concat(e),!0},n.prototype.insertToGallery=function(t){var e=this;h.each(t,function(){h(this).appendTo(e.$gallery)})},n.prototype.shuffleArray=function(t){var e,i,s;for(e=t.length-1;0<e;e--)i=Math.floor(Math.random()*(e+1)),s=t[e],t[e]=t[i],t[i]=s;return this.insertToGallery(t),t},n.prototype.sortArray=function(t){return t.sort(this.settings.sort),this.insertToGallery(t),t},n.prototype.resetFilters=function(t){for(var e=0;e<t.length;e++)h(t[e]).removeClass("jg-filtered")},n.prototype.filterArray=function(t){var i=this.settings;if("string"===h.type(i.filter))return t.filter(function(t){var e=h(t);return e.is(i.filter)?(e.removeClass("jg-filtered"),!0):(e.addClass("jg-filtered").removeClass("jg-visible"),!1)});if(h.isFunction(i.filter)){for(var e=t.filter(i.filter),s=0;s<t.length;s++)-1===e.indexOf(t[s])?h(t[s]).addClass("jg-filtered").removeClass("jg-visible"):h(t[s]).removeClass("jg-filtered");return e}},n.prototype.resetImgSrc=function(t){"src"==t.data("jg.originalSrcLoc")?t.attr("src",t.data("jg.originalSrc")):t.attr("src","")},n.prototype.destroy=function(){clearInterval(this.checkWidthIntervalId),this.stopImgAnalyzerStarter(),h.each(this.getAllEntries(),h.proxy(function(t,e){var i=h(e);i.css("width",""),i.css("height",""),i.css("top",""),i.css("left",""),i.data("jg.loaded",void 0),i.removeClass("jg-entry jg-filtered jg-entry-visible");var s=this.imgFromEntry(i);s&&(s.css("width",""),s.css("height",""),s.css("margin-left",""),s.css("margin-top",""),this.resetImgSrc(s),s.data("jg.originalSrc",void 0),s.data("jg.originalSrcLoc",void 0),s.data("jg.src",void 0)),this.removeCaptionEventsHandlers(i);var o=this.captionFromEntry(i);i.data("jg.createdCaption")?(i.data("jg.createdCaption",void 0),null!==o&&o.remove()):null!==o&&o.fadeTo(0,1)},this)),this.$gallery.css("height",""),this.$gallery.removeClass("justified-gallery"),this.$gallery.data("jg.controller",void 0),this.settings.triggerEvent.call(this,"jg.destroy")},n.prototype.analyzeImages=function(t){for(var e=this.lastAnalyzedIndex+1;e<this.entries.length;e++){var i=h(this.entries[e]);if(!0===i.data("jg.loaded")||"skipped"===i.data("jg.loaded")){var s=this.galleryWidth-2*this.border-(this.buildingRow.entriesBuff.length-1)*this.settings.margins,o=i.data("jg.width")/i.data("jg.height");if(this.buildingRow.entriesBuff.push(i),this.buildingRow.aspectRatio+=o,this.buildingRow.width+=o*this.settings.rowHeight,this.lastAnalyzedIndex=e,s/(this.buildingRow.aspectRatio+o)<this.settings.rowHeight&&(this.flushRow(!1),++this.yield.flushed>=this.yield.every))return void this.startImgAnalyzer(t)}else if("error"!==i.data("jg.loaded"))return}0<this.buildingRow.entriesBuff.length&&this.flushRow(!0),this.isSpinnerActive()&&this.stopLoadingSpinnerAnimation(),this.stopImgAnalyzerStarter(),this.settings.triggerEvent.call(this,t?"jg.resize":"jg.complete"),this.setGalleryFinalHeight(this.galleryHeightToSet)},n.prototype.stopImgAnalyzerStarter=function(){this.yield.flushed=0,null!==this.imgAnalyzerTimeout&&(clearTimeout(this.imgAnalyzerTimeout),this.imgAnalyzerTimeout=null)},n.prototype.startImgAnalyzer=function(t){var e=this;this.stopImgAnalyzerStarter(),this.imgAnalyzerTimeout=setTimeout(function(){e.analyzeImages(t)},.001)},n.prototype.onImageEvent=function(t,e,i){if(e||i){var s=new Image,o=h(s);e&&o.one("load",function(){o.off("load error"),e(s)}),i&&o.one("error",function(){o.off("load error"),i(s)}),s.src=t}},n.prototype.init=function(){var a=!1,l=!1,d=this;h.each(this.entries,function(t,e){var i=h(e),s=d.imgFromEntry(i);if(i.addClass("jg-entry"),!0!==i.data("jg.loaded")&&"skipped"!==i.data("jg.loaded"))if(null!==d.settings.rel&&i.attr("rel",d.settings.rel),null!==d.settings.target&&i.attr("target",d.settings.target),null!==s){var o=d.extractImgSrcFromImage(s);if(!1===d.settings.waitThumbnailsLoad||!o){var n=parseFloat(s.attr("width")),r=parseFloat(s.attr("height"));if("svg"===s.prop("tagName")&&(n=parseFloat(s[0].getBBox().width),r=parseFloat(s[0].getBBox().height)),!isNaN(n)&&!isNaN(r))return i.data("jg.width",n),i.data("jg.height",r),i.data("jg.loaded","skipped"),l=!0,d.startImgAnalyzer(!1),!0}i.data("jg.loaded",!1),a=!0,d.isSpinnerActive()||d.startLoadingSpinnerAnimation(),d.onImageEvent(o,function(t){i.data("jg.width",t.width),i.data("jg.height",t.height),i.data("jg.loaded",!0),d.startImgAnalyzer(!1)},function(){i.data("jg.loaded","error"),d.startImgAnalyzer(!1)})}else i.data("jg.loaded",!0),i.data("jg.width",i.width()|parseFloat(i.css("width"))|1),i.data("jg.height",i.height()|parseFloat(i.css("height"))|1)}),a||l||this.startImgAnalyzer(!1),this.checkWidth()},n.prototype.checkOrConvertNumber=function(t,e){if("string"===h.type(t[e])&&(t[e]=parseFloat(t[e])),"number"!==h.type(t[e]))throw e+" must be a number";if(isNaN(t[e]))throw"invalid number for "+e},n.prototype.checkSizeRangesSuffixes=function(){if("object"!==h.type(this.settings.sizeRangeSuffixes))throw"sizeRangeSuffixes must be defined and must be an object";var t=[];for(var e in this.settings.sizeRangeSuffixes)this.settings.sizeRangeSuffixes.hasOwnProperty(e)&&t.push(e);for(var i={0:""},s=0;s<t.length;s++)if("string"===h.type(t[s]))try{i[parseInt(t[s].replace(/^[a-z]+/,""),10)]=this.settings.sizeRangeSuffixes[t[s]]}catch(t){throw"sizeRangeSuffixes keys must contains correct numbers ("+t+")"}else i[t[s]]=this.settings.sizeRangeSuffixes[t[s]];this.settings.sizeRangeSuffixes=i},n.prototype.retrieveMaxRowHeight=function(){var t=null,e=this.settings.rowHeight;if("string"===h.type(this.settings.maxRowHeight))t=this.settings.maxRowHeight.match(/^[0-9]+%$/)?e*parseFloat(this.settings.maxRowHeight.match(/^([0-9]+)%$/)[1])/100:parseFloat(this.settings.maxRowHeight);else{if("number"!==h.type(this.settings.maxRowHeight)){if(!1===this.settings.maxRowHeight||null==this.settings.maxRowHeight)return null;throw"maxRowHeight must be a number or a percentage"}t=this.settings.maxRowHeight}if(isNaN(t))throw"invalid number for maxRowHeight";return t<e&&(t=e),t},n.prototype.checkSettings=function(){this.checkSizeRangesSuffixes(),this.checkOrConvertNumber(this.settings,"rowHeight"),this.checkOrConvertNumber(this.settings,"margins"),this.checkOrConvertNumber(this.settings,"border");var t=["justify","nojustify","left","center","right","hide"];if(-1===t.indexOf(this.settings.lastRow))throw"lastRow must be one of: "+t.join(", ");if(this.checkOrConvertNumber(this.settings,"justifyThreshold"),this.settings.justifyThreshold<0||1<this.settings.justifyThreshold)throw"justifyThreshold must be in the interval [0,1]";if("boolean"!==h.type(this.settings.cssAnimation))throw"cssAnimation must be a boolean";if("boolean"!==h.type(this.settings.captions))throw"captions must be a boolean";if(this.checkOrConvertNumber(this.settings.captionSettings,"animationDuration"),this.checkOrConvertNumber(this.settings.captionSettings,"visibleOpacity"),this.settings.captionSettings.visibleOpacity<0||1<this.settings.captionSettings.visibleOpacity)throw"captionSettings.visibleOpacity must be in the interval [0, 1]";if(this.checkOrConvertNumber(this.settings.captionSettings,"nonVisibleOpacity"),this.settings.captionSettings.nonVisibleOpacity<0||1<this.settings.captionSettings.nonVisibleOpacity)throw"captionSettings.nonVisibleOpacity must be in the interval [0, 1]";if(this.checkOrConvertNumber(this.settings,"imagesAnimationDuration"),this.checkOrConvertNumber(this.settings,"refreshTime"),this.checkOrConvertNumber(this.settings,"refreshSensitivity"),"boolean"!==h.type(this.settings.randomize))throw"randomize must be a boolean";if("string"!==h.type(this.settings.selector))throw"selector must be a string";if(!1!==this.settings.sort&&!h.isFunction(this.settings.sort))throw"sort must be false or a comparison function";if(!1!==this.settings.filter&&!h.isFunction(this.settings.filter)&&"string"!==h.type(this.settings.filter))throw"filter must be false, a string or a filter function"},n.prototype.retrieveSuffixRanges=function(){var t=[];for(var e in this.settings.sizeRangeSuffixes)this.settings.sizeRangeSuffixes.hasOwnProperty(e)&&t.push(parseInt(e,10));return t.sort(function(t,e){return e<t?1:t<e?-1:0}),t},n.prototype.updateSettings=function(t){this.settings=h.extend({},this.settings,t),this.checkSettings(),this.border=0<=this.settings.border?this.settings.border:this.settings.margins,this.maxRowHeight=this.retrieveMaxRowHeight(),this.suffixRanges=this.retrieveSuffixRanges()},n.prototype.defaults={sizeRangeSuffixes:{},thumbnailPath:void 0,rowHeight:120,maxRowHeight:!1,margins:1,border:-1,lastRow:"nojustify",justifyThreshold:.9,waitThumbnailsLoad:!0,captions:!0,cssAnimation:!0,imagesAnimationDuration:500,captionSettings:{animationDuration:500,visibleOpacity:.7,nonVisibleOpacity:0},rel:null,target:null,extension:/\.[^.\\/]+$/,refreshTime:200,refreshSensitivity:0,randomize:!1,rtl:!1,sort:!1,filter:!1,selector:"a, div:not(.spinner)",imgSelector:"> img, > a > img, > svg, > a > svg",triggerEvent:function(t){this.$gallery.trigger(t)}},h.fn.justifiedGallery=function(o){return this.each(function(t,e){var i=h(e);i.addClass("justified-gallery");var s=i.data("jg.controller");if(void 0===s){if(null!=o&&"object"!==h.type(o)){if("destroy"===o)return;throw"The argument must be an object"}s=new n(i,h.extend({},n.prototype.defaults,o)),i.data("jg.controller",s)}else if("norewind"===o);else{if("destroy"===o)return void s.destroy();s.updateSettings(o),s.rewind()}s.updateEntries("norewind"===o)&&s.init()})}}),function(t,e){"function"==typeof define&&define.amd?define(["jquery"],function(t){return e(t)}):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(t.jQuery)}(this,function(g){!function(){"use strict";function e(t,e){if(this.el=t,this.$el=g(t),this.s=g.extend({},i,e),this.s.dynamic&&"undefined"!==this.s.dynamicEl&&this.s.dynamicEl.constructor===Array&&!this.s.dynamicEl.length)throw"When using dynamic mode, you must also define dynamicEl as an Array.";return this.modules={},this.lGalleryOn=!1,this.lgBusy=!1,this.hideBartimeout=!1,this.isTouch="ontouchstart"in document.documentElement,this.s.slideEndAnimatoin&&(this.s.hideControlOnEnd=!1),this.s.dynamic?this.$items=this.s.dynamicEl:"this"===this.s.selector?this.$items=this.$el:""!==this.s.selector?this.s.selectWithin?this.$items=g(this.s.selectWithin).find(this.s.selector):this.$items=this.$el.find(g(this.s.selector)):this.$items=this.$el.children(),this.$slide="",this.$outer="",this.init(),this}var i={mode:"lg-slide",cssEasing:"ease",easing:"linear",speed:600,height:"100%",width:"100%",addClass:"",startClass:"lg-start-zoom",backdropDuration:150,hideBarsDelay:6e3,useLeft:!1,closable:!0,loop:!0,escKey:!0,keyPress:!0,controls:!0,slideEndAnimatoin:!0,hideControlOnEnd:!1,mousewheel:!0,getCaptionFromTitleOrAlt:!0,appendSubHtmlTo:".lg-sub-html",subHtmlSelectorRelative:!1,preload:1,showAfterLoad:!0,selector:"",selectWithin:"",nextHtml:"",prevHtml:"",index:!1,iframeMaxWidth:"100%",download:!0,counter:!0,appendCounterTo:".lg-toolbar",swipeThreshold:50,enableSwipe:!0,enableDrag:!0,dynamic:!1,dynamicEl:[],galleryId:1};e.prototype.init=function(){var t=this;t.s.preload>t.$items.length&&(t.s.preload=t.$items.length);var e=window.location.hash;0<e.indexOf("lg="+this.s.galleryId)&&(t.index=parseInt(e.split("&slide=")[1],10),g("body").addClass("lg-from-hash"),g("body").hasClass("lg-on")||(setTimeout(function(){t.build(t.index)}),g("body").addClass("lg-on"))),t.s.dynamic?(t.$el.trigger("onBeforeOpen.lg"),t.index=t.s.index||0,g("body").hasClass("lg-on")||setTimeout(function(){t.build(t.index),g("body").addClass("lg-on")})):t.$items.on("click.lgcustom",function(e){try{e.preventDefault(),e.preventDefault()}catch(t){e.returnValue=!1}t.$el.trigger("onBeforeOpen.lg"),t.index=t.s.index||t.$items.index(this),g("body").hasClass("lg-on")||(t.build(t.index),g("body").addClass("lg-on"))})},e.prototype.build=function(t){var e=this;e.structure(),g.each(g.fn.lightGallery.modules,function(t){e.modules[t]=new g.fn.lightGallery.modules[t](e.el)}),e.slide(t,!1,!1,!1),e.s.keyPress&&e.keyPress(),1<e.$items.length?(e.arrow(),setTimeout(function(){e.enableDrag(),e.enableSwipe()},50),e.s.mousewheel&&e.mousewheel()):e.$slide.on("click.lg",function(){e.$el.trigger("onSlideClick.lg")}),e.counter(),e.closeGallery(),e.$el.trigger("onAfterOpen.lg"),e.$outer.on("mousemove.lg click.lg touchstart.lg",function(){e.$outer.removeClass("lg-hide-items"),clearTimeout(e.hideBartimeout),e.hideBartimeout=setTimeout(function(){e.$outer.addClass("lg-hide-items")},e.s.hideBarsDelay)}),e.$outer.trigger("mousemove.lg")},e.prototype.structure=function(){var t,e="",i="",s=0,o="",n=this;for(g("body").append('<div class="lg-backdrop"></div>'),g(".lg-backdrop").css("transition-duration",this.s.backdropDuration+"ms"),s=0;s<this.$items.length;s++)e+='<div class="lg-item"></div>';if(this.s.controls&&1<this.$items.length&&(i='<div class="lg-actions"><button class="lg-prev lg-icon">'+this.s.prevHtml+'</button><button class="lg-next lg-icon">'+this.s.nextHtml+"</button></div>"),".lg-sub-html"===this.s.appendSubHtmlTo&&(o='<div class="lg-sub-html"></div>'),t='<div class="lg-outer '+this.s.addClass+" "+this.s.startClass+'"><div class="lg" style="width:'+this.s.width+"; height:"+this.s.height+'"><div class="lg-inner">'+e+'</div><div class="lg-toolbar lg-group"><span class="lg-close lg-icon"></span></div>'+i+o+"</div></div>",g("body").append(t),this.$outer=g(".lg-outer"),this.$slide=this.$outer.find(".lg-item"),this.s.useLeft?(this.$outer.addClass("lg-use-left"),this.s.mode="lg-slide"):this.$outer.addClass("lg-use-css3"),n.setTop(),g(window).on("resize.lg orientationchange.lg",function(){setTimeout(function(){n.setTop()},100)}),this.$slide.eq(this.index).addClass("lg-current"),this.doCss()?this.$outer.addClass("lg-css3"):(this.$outer.addClass("lg-css"),this.s.speed=0),this.$outer.addClass(this.s.mode),this.s.enableDrag&&1<this.$items.length&&this.$outer.addClass("lg-grab"),this.s.showAfterLoad&&this.$outer.addClass("lg-show-after-load"),this.doCss()){var r=this.$outer.find(".lg-inner");r.css("transition-timing-function",this.s.cssEasing),r.css("transition-duration",this.s.speed+"ms")}setTimeout(function(){g(".lg-backdrop").addClass("in")}),setTimeout(function(){n.$outer.addClass("lg-visible")},this.s.backdropDuration),this.s.download&&this.$outer.find(".lg-toolbar").append('<a id="lg-download" target="_blank" download class="lg-download lg-icon"></a>'),this.prevScrollTop=g(window).scrollTop()},e.prototype.setTop=function(){if("100%"!==this.s.height){var t=g(window).height(),e=(t-parseInt(this.s.height,10))/2,i=this.$outer.find(".lg");t>=parseInt(this.s.height,10)?i.css("top",e+"px"):i.css("top","0px")}},e.prototype.doCss=function(){return!!function(){var t=["transition","MozTransition","WebkitTransition","OTransition","msTransition","KhtmlTransition"],e=document.documentElement,i=0;for(i=0;i<t.length;i++)if(t[i]in e.style)return!0}()},e.prototype.isVideo=function(t,e){var i;if(i=this.s.dynamic?this.s.dynamicEl[e].html:this.$items.eq(e).attr("data-html"),!t)return i?{html5:!0}:(console.error("lightGallery :- data-src is not pvovided on slide item "+(e+1)+". Please make sure the selector property is properly configured. More info - http://sachinchoolur.github.io/lightGallery/demos/html-markup.html"),!1);var s=t.match(/\/\/(?:www\.)?youtu(?:\.be|be\.com|be-nocookie\.com)\/(?:watch\?v=|embed\/)?([a-z0-9\-\_\%]+)/i),o=t.match(/\/\/(?:www\.)?vimeo.com\/([0-9a-z\-_]+)/i),n=t.match(/\/\/(?:www\.)?dai.ly\/([0-9a-z\-_]+)/i),r=t.match(/\/\/(?:www\.)?(?:vk\.com|vkontakte\.ru)\/(?:video_ext\.php\?)(.*)/i);return s?{youtube:s}:o?{vimeo:o}:n?{dailymotion:n}:r?{vk:r}:void 0},e.prototype.counter=function(){this.s.counter&&g(this.s.appendCounterTo).append('<div id="lg-counter"><span id="lg-counter-current">'+(parseInt(this.index,10)+1)+'</span> / <span id="lg-counter-all">'+this.$items.length+"</span></div>")},e.prototype.addHtml=function(t){var e,i,s=null;if(this.s.dynamic?this.s.dynamicEl[t].subHtmlUrl?e=this.s.dynamicEl[t].subHtmlUrl:s=this.s.dynamicEl[t].subHtml:(i=this.$items.eq(t)).attr("data-sub-html-url")?e=i.attr("data-sub-html-url"):(s=i.attr("data-sub-html"),this.s.getCaptionFromTitleOrAlt&&!s&&(s=i.attr("title")||i.find("img").first().attr("alt"))),!e)if(null!=s){var o=s.substring(0,1);"."!==o&&"#"!==o||(s=this.s.subHtmlSelectorRelative&&!this.s.dynamic?i.find(s).html():g(s).html())}else s="";".lg-sub-html"===this.s.appendSubHtmlTo?e?this.$outer.find(this.s.appendSubHtmlTo).load(e):this.$outer.find(this.s.appendSubHtmlTo).html(s):e?this.$slide.eq(t).load(e):this.$slide.eq(t).append(s),null!=s&&(""===s?this.$outer.find(this.s.appendSubHtmlTo).addClass("lg-empty-html"):this.$outer.find(this.s.appendSubHtmlTo).removeClass("lg-empty-html")),this.$el.trigger("onAfterAppendSubHtml.lg",[t])},e.prototype.preload=function(t){var e=1,i=1;for(e=1;e<=this.s.preload&&!(e>=this.$items.length-t);e++)this.loadContent(t+e,!1,0);for(i=1;i<=this.s.preload&&!(t-i<0);i++)this.loadContent(t-i,!1,0)},e.prototype.loadContent=function(e,t,i){function s(t){for(var e=[],i=[],s=0;s<t.length;s++){var o=t[s].split(" ");""===o[0]&&o.splice(0,1),i.push(o[0]),e.push(o[1])}for(var n=g(window).width(),r=0;r<e.length;r++)if(parseInt(e[r],10)>n){a=i[r];break}}var o,a,n,r,l,d,h=this,c=!1;l=h.s.dynamic?(h.s.dynamicEl[e].poster&&(c=!0,n=h.s.dynamicEl[e].poster),d=h.s.dynamicEl[e].html,a=h.s.dynamicEl[e].src,h.s.dynamicEl[e].responsive&&s(h.s.dynamicEl[e].responsive.split(",")),r=h.s.dynamicEl[e].srcset,h.s.dynamicEl[e].sizes):(h.$items.eq(e).attr("data-poster")&&(c=!0,n=h.$items.eq(e).attr("data-poster")),d=h.$items.eq(e).attr("data-html"),a=h.$items.eq(e).attr("href")||h.$items.eq(e).attr("data-src"),h.$items.eq(e).attr("data-responsive")&&s(h.$items.eq(e).attr("data-responsive").split(",")),r=h.$items.eq(e).attr("data-srcset"),h.$items.eq(e).attr("data-sizes"));var u=!1;h.s.dynamic?h.s.dynamicEl[e].iframe&&(u=!0):"true"===h.$items.eq(e).attr("data-iframe")&&(u=!0);var p=h.isVideo(a,e);if(!h.$slide.eq(e).hasClass("lg-loaded")){if(u)h.$slide.eq(e).prepend('<div class="lg-video-cont lg-has-iframe" style="max-width:'+h.s.iframeMaxWidth+'"><div class="lg-video"><iframe class="lg-object" frameborder="0" src="'+a+'"  allowfullscreen="true"></iframe></div></div>');else if(c){var f;f=p&&p.youtube?"lg-has-youtube":p&&p.vimeo?"lg-has-vimeo":"lg-has-html5",h.$slide.eq(e).prepend('<div class="lg-video-cont '+f+' "><div class="lg-video"><span class="lg-video-play"></span><img class="lg-object lg-has-poster" src="'+n+'" /></div></div>')}else p?(h.$slide.eq(e).prepend('<div class="lg-video-cont "><div class="lg-video"></div></div>'),h.$el.trigger("hasVideo.lg",[e,a,d])):h.$slide.eq(e).prepend('<div class="lg-img-wrap"><img class="lg-object lg-image" src="'+a+'" /></div>');if(h.$el.trigger("onAferAppendSlide.lg",[e]),o=h.$slide.eq(e).find(".lg-object"),l&&o.attr("sizes",l),r){o.attr("srcset",r);try{picturefill({elements:[o[0]]})}catch(t){console.warn("lightGallery :- If you want srcset to be supported for older browser please include picturefil version 2 javascript library in your document.")}}".lg-sub-html"!==this.s.appendSubHtmlTo&&h.addHtml(e),h.$slide.eq(e).addClass("lg-loaded")}h.$slide.eq(e).find(".lg-object").on("load.lg error.lg",function(){var t=0;i&&!g("body").hasClass("lg-from-hash")&&(t=i),setTimeout(function(){h.$slide.eq(e).addClass("lg-complete"),h.$el.trigger("onSlideItemLoad.lg",[e,i||0])},t)}),p&&p.html5&&!c&&h.$slide.eq(e).addClass("lg-complete"),!0===t&&(h.$slide.eq(e).hasClass("lg-complete")?h.preload(e):h.$slide.eq(e).find(".lg-object").on("load.lg error.lg",function(){h.preload(e)}))},e.prototype.slide=function(t,e,i,s){var o=this.$outer.find(".lg-current").index(),n=this;if(!n.lGalleryOn||o!==t){var r=this.$slide.length,a=n.lGalleryOn?this.s.speed:0;if(!n.lgBusy){var l,d,h;if(this.s.download)(l=n.s.dynamic?!1!==n.s.dynamicEl[t].downloadUrl&&(n.s.dynamicEl[t].downloadUrl||n.s.dynamicEl[t].src):"false"!==n.$items.eq(t).attr("data-download-url")&&(n.$items.eq(t).attr("data-download-url")||n.$items.eq(t).attr("href")||n.$items.eq(t).attr("data-src")))?(g("#lg-download").attr("href",l),n.$outer.removeClass("lg-hide-download")):n.$outer.addClass("lg-hide-download");if(this.$el.trigger("onBeforeSlide.lg",[o,t,e,i]),n.lgBusy=!0,clearTimeout(n.hideBartimeout),".lg-sub-html"===this.s.appendSubHtmlTo&&setTimeout(function(){n.addHtml(t)},a),this.arrowDisable(t),s||(t<o?s="prev":o<t&&(s="next")),e)this.$slide.removeClass("lg-prev-slide lg-current lg-next-slide"),2<r?(d=t-1,h=t+1,0===t&&o===r-1?(h=0,d=r-1):t===r-1&&0===o&&(h=0,d=r-1)):(d=0,h=1),"prev"===s?n.$slide.eq(h).addClass("lg-next-slide"):n.$slide.eq(d).addClass("lg-prev-slide"),n.$slide.eq(t).addClass("lg-current");else n.$outer.addClass("lg-no-trans"),this.$slide.removeClass("lg-prev-slide lg-next-slide"),"prev"===s?(this.$slide.eq(t).addClass("lg-prev-slide"),this.$slide.eq(o).addClass("lg-next-slide")):(this.$slide.eq(t).addClass("lg-next-slide"),this.$slide.eq(o).addClass("lg-prev-slide")),setTimeout(function(){n.$slide.removeClass("lg-current"),n.$slide.eq(t).addClass("lg-current"),n.$outer.removeClass("lg-no-trans")},50);n.lGalleryOn?(setTimeout(function(){n.loadContent(t,!0,0)},this.s.speed+50),setTimeout(function(){n.lgBusy=!1,n.$el.trigger("onAfterSlide.lg",[o,t,e,i])},this.s.speed)):(n.loadContent(t,!0,n.s.backdropDuration),n.lgBusy=!1,n.$el.trigger("onAfterSlide.lg",[o,t,e,i])),n.lGalleryOn=!0,this.s.counter&&g("#lg-counter-current").text(t+1)}n.index=t}},e.prototype.goToNextSlide=function(t){var e=this,i=e.s.loop;t&&e.$slide.length<3&&(i=!1),e.lgBusy||(e.index+1<e.$slide.length?(e.index++,e.$el.trigger("onBeforeNextSlide.lg",[e.index]),e.slide(e.index,t,!1,"next")):i?(e.index=0,e.$el.trigger("onBeforeNextSlide.lg",[e.index]),e.slide(e.index,t,!1,"next")):e.s.slideEndAnimatoin&&!t&&(e.$outer.addClass("lg-right-end"),setTimeout(function(){e.$outer.removeClass("lg-right-end")},400)))},e.prototype.goToPrevSlide=function(t){var e=this,i=e.s.loop;t&&e.$slide.length<3&&(i=!1),e.lgBusy||(0<e.index?(e.index--,e.$el.trigger("onBeforePrevSlide.lg",[e.index,t]),e.slide(e.index,t,!1,"prev")):i?(e.index=e.$items.length-1,e.$el.trigger("onBeforePrevSlide.lg",[e.index,t]),e.slide(e.index,t,!1,"prev")):e.s.slideEndAnimatoin&&!t&&(e.$outer.addClass("lg-left-end"),setTimeout(function(){e.$outer.removeClass("lg-left-end")},400)))},e.prototype.keyPress=function(){var e=this;1<this.$items.length&&g(window).on("keyup.lg",function(t){1<e.$items.length&&(37===t.keyCode&&(t.preventDefault(),e.goToPrevSlide()),39===t.keyCode&&(t.preventDefault(),e.goToNextSlide()))}),g(window).on("keydown.lg",function(t){!0===e.s.escKey&&27===t.keyCode&&(t.preventDefault(),e.$outer.hasClass("lg-thumb-open")?e.$outer.removeClass("lg-thumb-open"):e.destroy())})},e.prototype.arrow=function(){var t=this;this.$outer.find(".lg-prev").on("click.lg",function(){t.goToPrevSlide()}),this.$outer.find(".lg-next").on("click.lg",function(){t.goToNextSlide()})},e.prototype.arrowDisable=function(t){!this.s.loop&&this.s.hideControlOnEnd&&(t+1<this.$slide.length?this.$outer.find(".lg-next").removeAttr("disabled").removeClass("disabled"):this.$outer.find(".lg-next").attr("disabled","disabled").addClass("disabled"),0<t?this.$outer.find(".lg-prev").removeAttr("disabled").removeClass("disabled"):this.$outer.find(".lg-prev").attr("disabled","disabled").addClass("disabled"))},e.prototype.setTranslate=function(t,e,i){this.s.useLeft?t.css("left",e):t.css({transform:"translate3d("+e+"px, "+i+"px, 0px)"})},e.prototype.touchMove=function(t,e){var i=e-t;15<Math.abs(i)&&(this.$outer.addClass("lg-dragging"),this.setTranslate(this.$slide.eq(this.index),i,0),this.setTranslate(g(".lg-prev-slide"),-this.$slide.eq(this.index).width()+i,0),this.setTranslate(g(".lg-next-slide"),this.$slide.eq(this.index).width()+i,0))},e.prototype.touchEnd=function(t){var e=this;"lg-slide"!==e.s.mode&&e.$outer.addClass("lg-slide"),this.$slide.not(".lg-current, .lg-prev-slide, .lg-next-slide").css("opacity","0"),setTimeout(function(){e.$outer.removeClass("lg-dragging"),t<0&&Math.abs(t)>e.s.swipeThreshold?e.goToNextSlide(!0):0<t&&Math.abs(t)>e.s.swipeThreshold?e.goToPrevSlide(!0):Math.abs(t)<5&&e.$el.trigger("onSlideClick.lg"),e.$slide.removeAttr("style")}),setTimeout(function(){e.$outer.hasClass("lg-dragging")||"lg-slide"===e.s.mode||e.$outer.removeClass("lg-slide")},e.s.speed+100)},e.prototype.enableSwipe=function(){var e=this,i=0,s=0,o=!1;e.s.enableSwipe&&e.doCss()&&(e.$slide.on("touchstart.lg",function(t){e.$outer.hasClass("lg-zoomed")||e.lgBusy||(t.preventDefault(),e.manageSwipeClass(),i=t.originalEvent.targetTouches[0].pageX)}),e.$slide.on("touchmove.lg",function(t){e.$outer.hasClass("lg-zoomed")||(t.preventDefault(),s=t.originalEvent.targetTouches[0].pageX,e.touchMove(i,s),o=!0)}),e.$slide.on("touchend.lg",function(){e.$outer.hasClass("lg-zoomed")||(o?(o=!1,e.touchEnd(s-i)):e.$el.trigger("onSlideClick.lg"))}))},e.prototype.enableDrag=function(){var e=this,i=0,s=0,o=!1,n=!1;e.s.enableDrag&&e.doCss()&&(e.$slide.on("mousedown.lg",function(t){e.$outer.hasClass("lg-zoomed")||e.lgBusy||g(t.target).text().trim()||(t.preventDefault(),e.manageSwipeClass(),i=t.pageX,o=!0,e.$outer.scrollLeft+=1,e.$outer.scrollLeft-=1,e.$outer.removeClass("lg-grab").addClass("lg-grabbing"),e.$el.trigger("onDragstart.lg"))}),g(window).on("mousemove.lg",function(t){o&&(n=!0,s=t.pageX,e.touchMove(i,s),e.$el.trigger("onDragmove.lg"))}),g(window).on("mouseup.lg",function(t){n?(n=!1,e.touchEnd(s-i),e.$el.trigger("onDragend.lg")):(g(t.target).hasClass("lg-object")||g(t.target).hasClass("lg-video-play"))&&e.$el.trigger("onSlideClick.lg"),o&&(o=!1,e.$outer.removeClass("lg-grabbing").addClass("lg-grab"))}))},e.prototype.manageSwipeClass=function(){var t=this.index+1,e=this.index-1;this.s.loop&&2<this.$slide.length&&(0===this.index?e=this.$slide.length-1:this.index===this.$slide.length-1&&(t=0)),this.$slide.removeClass("lg-next-slide lg-prev-slide"),-1<e&&this.$slide.eq(e).addClass("lg-prev-slide"),this.$slide.eq(t).addClass("lg-next-slide")},e.prototype.mousewheel=function(){var e=this;e.$outer.on("mousewheel.lg",function(t){t.deltaY&&(0<t.deltaY?e.goToPrevSlide():e.goToNextSlide(),t.preventDefault())})},e.prototype.closeGallery=function(){var e=this,i=!1;this.$outer.find(".lg-close").on("click.lg",function(){e.destroy()}),e.s.closable&&(e.$outer.on("mousedown.lg",function(t){i=!!(g(t.target).is(".lg-outer")||g(t.target).is(".lg-item ")||g(t.target).is(".lg-img-wrap"))}),e.$outer.on("mousemove.lg",function(){i=!1}),e.$outer.on("mouseup.lg",function(t){(g(t.target).is(".lg-outer")||g(t.target).is(".lg-item ")||g(t.target).is(".lg-img-wrap")&&i)&&(e.$outer.hasClass("lg-dragging")||e.destroy())}))},e.prototype.destroy=function(t){var e=this;t||(e.$el.trigger("onBeforeClose.lg"),g(window).scrollTop(e.prevScrollTop)),t&&(e.s.dynamic||this.$items.off("click.lg click.lgcustom"),g.removeData(e.el,"lightGallery")),this.$el.off(".lg.tm"),g.each(g.fn.lightGallery.modules,function(t){e.modules[t]&&e.modules[t].destroy()}),this.lGalleryOn=!1,clearTimeout(e.hideBartimeout),this.hideBartimeout=!1,g(window).off(".lg"),g("body").removeClass("lg-on lg-from-hash"),e.$outer&&e.$outer.removeClass("lg-visible"),g(".lg-backdrop").removeClass("in"),setTimeout(function(){e.$outer&&e.$outer.remove(),g(".lg-backdrop").remove(),t||e.$el.trigger("onCloseAfter.lg")},e.s.backdropDuration+50)},g.fn.lightGallery=function(t){return this.each(function(){if(g.data(this,"lightGallery"))try{g(this).data("lightGallery").init()}catch(t){console.error("lightGallery has not initiated properly")}else g.data(this,"lightGallery",new e(this,t))})},g.fn.lightGallery.modules={}}()}),function(d){"use strict";d.ajaxChimp={responses:{"We have sent you a confirmation email":0,"Please enter a value":1,"An email address must contain a single @":2,"The domain portion of the email address is invalid (the portion after the @: )":3,"The username portion of the email address is invalid (the portion before the @: )":4,"This email address looks fake or invalid. Please enter a real email address":5},translations:{en:null},init:function(t,e){d(t).ajaxChimp(e)}},d.fn.ajaxChimp=function(i){return d(this).each(function(t,e){var o=d(e),n=o.find("input[type=email]"),r=o.find("label[for="+n.attr("id")+"]"),a=d.extend({url:o.attr("action"),language:"en"},i),l=a.url.replace("/post?","/post-json?").concat("&c=?");o.attr("novalidate","true"),n.attr("name","EMAIL"),o.submit(function(){var s;var i={},t=o.serializeArray();d.each(t,function(t,e){i[e.name]=e.value}),d.ajax({url:l,data:i,success:function(e){if("success"===e.result)s="We have sent you a confirmation email",r.removeClass("error").addClass("valid"),n.removeClass("error").addClass("valid");else{n.removeClass("valid").addClass("error"),r.removeClass("valid").addClass("error");try{var t=e.msg.split(" - ",2);if(void 0===t[1])s=e.msg;else{var i=parseInt(t[0],10);s=i.toString()===t[0]?(t[0],t[1]):(-1,e.msg)}}catch(t){-1,s=e.msg}}"en"!==a.language&&void 0!==d.ajaxChimp.responses[s]&&d.ajaxChimp.translations&&d.ajaxChimp.translations[a.language]&&d.ajaxChimp.translations[a.language][d.ajaxChimp.responses[s]]&&(s=d.ajaxChimp.translations[a.language][d.ajaxChimp.responses[s]]),r.html(s),r.show(2e3),a.callback&&a.callback(e)},dataType:"jsonp",error:function(t,e){console.log("mailchimp ajax submit error: "+e)}});var e="Submitting...";return"en"!==a.language&&d.ajaxChimp.translations&&d.ajaxChimp.translations[a.language]&&d.ajaxChimp.translations[a.language].submit&&(e=d.ajaxChimp.translations[a.language].submit),r.html(e).show(2e3),!1})}),this}}(jQuery),function(e,i){"function"==typeof define&&define.amd?define("jquery-bridget/jquery-bridget",["jquery"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("jquery")):e.jQueryBridget=i(e,e.jQuery)}(window,function(t,e){"use strict";function i(d,o,h){(h=h||e||t.jQuery)&&(o.prototype.option||(o.prototype.option=function(t){h.isPlainObject(t)&&(this.options=h.extend(!0,this.options,t))}),h.fn[d]=function(t){return"string"!=typeof t?(function(t,s){t.each(function(t,e){var i=h.data(e,d);i?(i.option(s),i._init()):(i=new o(e,s),h.data(e,d,i))})}(this,t),this):function(t,n,r){var a,l="$()."+d+'("'+n+'")';return t.each(function(t,e){var i=h.data(e,d);if(i){var s=i[n];if(s&&"_"!=n.charAt(0)){var o=s.apply(i,r);a=void 0===a?o:a}else c(l+" is not a valid method")}else c(d+" not initialized. Cannot call methods, i.e. "+l)}),void 0!==a?a:t}(this,t,n.call(arguments,1))},s(h))}function s(t){!t||t&&t.bridget||(t.bridget=i)}var n=Array.prototype.slice,o=t.console,c=void 0===o?function(){}:function(t){o.error(t)};return s(e||t.jQuery),i}),function(t,e){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",e):"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()}("undefined"!=typeof window?window:this,function(){function t(){}var e=t.prototype;return e.on=function(t,e){if(t&&e){var i=this._events=this._events||{},s=i[t]=i[t]||[];return-1==s.indexOf(e)&&s.push(e),this}},e.once=function(t,e){if(t&&e){this.on(t,e);var i=this._onceEvents=this._onceEvents||{};return(i[t]=i[t]||{})[e]=!0,this}},e.off=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){var s=i.indexOf(e);return-1!=s&&i.splice(s,1),this}},e.emitEvent=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){i=i.slice(0),e=e||[];for(var s=this._onceEvents&&this._onceEvents[t],o=0;o<i.length;o++){var n=i[o];s&&s[n]&&(this.off(t,n),delete s[n]),n.apply(this,e)}return this}},e.allOff=function(){delete this._events,delete this._onceEvents},t}),function(t,e){"function"==typeof define&&define.amd?define("get-size/get-size",e):"object"==typeof module&&module.exports?module.exports=e():t.getSize=e()}(window,function(){"use strict";function y(t){var e=parseFloat(t);return-1==t.indexOf("%")&&!isNaN(e)&&e}function v(t){var e=getComputedStyle(t);return e||i("Style returned "+e+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),e}function w(t){if(function(){if(!k){k=!0;var t=document.createElement("div");t.style.width="200px",t.style.padding="1px 2px 3px 4px",t.style.borderStyle="solid",t.style.borderWidth="1px 2px 3px 4px",t.style.boxSizing="border-box";var e=document.body||document.documentElement;e.appendChild(t);var i=v(t);b=200==Math.round(y(i.width)),w.isBoxSizeOuter=b,e.removeChild(t)}}(),"string"==typeof t&&(t=document.querySelector(t)),t&&"object"==typeof t&&t.nodeType){var e=v(t);if("none"==e.display)return function(){for(var t={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},e=0;e<T;e++){t[S[e]]=0}return t}();var i={};i.width=t.offsetWidth,i.height=t.offsetHeight;for(var s=i.isBorderBox="border-box"==e.boxSizing,o=0;o<T;o++){var n=S[o],r=e[n],a=parseFloat(r);i[n]=isNaN(a)?0:a}var l=i.paddingLeft+i.paddingRight,d=i.paddingTop+i.paddingBottom,h=i.marginLeft+i.marginRight,c=i.marginTop+i.marginBottom,u=i.borderLeftWidth+i.borderRightWidth,p=i.borderTopWidth+i.borderBottomWidth,f=s&&b,g=y(e.width);!1!==g&&(i.width=g+(f?0:l+u));var m=y(e.height);return!1!==m&&(i.height=m+(f?0:d+p)),i.innerWidth=i.width-(l+u),i.innerHeight=i.height-(d+p),i.outerWidth=i.width+h,i.outerHeight=i.height+c,i}}var b,i="undefined"==typeof console?function(){}:function(t){console.error(t)},S=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],T=S.length,k=!1;return w}),function(t,e){"use strict";"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",e):"object"==typeof module&&module.exports?module.exports=e():t.matchesSelector=e()}(window,function(){"use strict";var i=function(){var t=window.Element.prototype;if(t.matches)return"matches";if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],i=0;i<e.length;i++){var s=e[i]+"MatchesSelector";if(t[s])return s}}();return function(t,e){return t[i](e)}}),function(e,i){"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],function(t){return i(e,t)}):"object"==typeof module&&module.exports?module.exports=i(e,require("desandro-matches-selector")):e.fizzyUIUtils=i(e,e.matchesSelector)}(window,function(d,n){var h={extend:function(t,e){for(var i in e)t[i]=e[i];return t},modulo:function(t,e){return(t%e+e)%e}},e=Array.prototype.slice;h.makeArray=function(t){return Array.isArray(t)?t:null==t?[]:"object"==typeof t&&"number"==typeof t.length?e.call(t):[t]},h.removeFrom=function(t,e){var i=t.indexOf(e);-1!=i&&t.splice(i,1)},h.getParent=function(t,e){for(;t.parentNode&&t!=document.body;)if(t=t.parentNode,n(t,e))return t},h.getQueryElement=function(t){return"string"==typeof t?document.querySelector(t):t},h.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},h.filterFindElements=function(t,s){t=h.makeArray(t);var o=[];return t.forEach(function(t){if(t instanceof HTMLElement){if(!s)return void o.push(t);n(t,s)&&o.push(t);for(var e=t.querySelectorAll(s),i=0;i<e.length;i++)o.push(e[i])}}),o},h.debounceMethod=function(t,e,s){s=s||100;var o=t.prototype[e],n=e+"Timeout";t.prototype[e]=function(){var t=this[n];clearTimeout(t);var e=arguments,i=this;this[n]=setTimeout(function(){o.apply(i,e),delete i[n]},s)}},h.docReady=function(t){var e=document.readyState;"complete"==e||"interactive"==e?setTimeout(t):document.addEventListener("DOMContentLoaded",t)},h.toDashed=function(t){return t.replace(/(.)([A-Z])/g,function(t,e,i){return e+"-"+i}).toLowerCase()};var c=d.console;return h.htmlInit=function(a,l){h.docReady(function(){var t=h.toDashed(l),o="data-"+t,e=document.querySelectorAll("["+o+"]"),i=document.querySelectorAll(".js-"+t),s=h.makeArray(e).concat(h.makeArray(i)),n=o+"-options",r=d.jQuery;s.forEach(function(e){var t,i=e.getAttribute(o)||e.getAttribute(n);try{t=i&&JSON.parse(i)}catch(t){return void(c&&c.error("Error parsing "+o+" on "+e.className+": "+t))}var s=new a(e,t);r&&r.data(e,l,s)})})},h}),function(t,e){"function"==typeof define&&define.amd?define("outlayer/item",["ev-emitter/ev-emitter","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("ev-emitter"),require("get-size")):(t.Outlayer={},t.Outlayer.Item=e(t.EvEmitter,t.getSize))}(window,function(t,e){"use strict";function i(t,e){t&&(this.element=t,this.layout=e,this.position={x:0,y:0},this._create())}var s=document.documentElement.style,o="string"==typeof s.transition?"transition":"WebkitTransition",n="string"==typeof s.transform?"transform":"WebkitTransform",r={WebkitTransition:"webkitTransitionEnd",transition:"transitionend"}[o],a={transform:n,transition:o,transitionDuration:o+"Duration",transitionProperty:o+"Property",transitionDelay:o+"Delay"},l=i.prototype=Object.create(t.prototype);l.constructor=i,l._create=function(){this._transn={ingProperties:{},clean:{},onEnd:{}},this.css({position:"absolute"})},l.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},l.getSize=function(){this.size=e(this.element)},l.css=function(t){var e=this.element.style;for(var i in t){e[a[i]||i]=t[i]}},l.getPosition=function(){var t=getComputedStyle(this.element),e=this.layout._getOption("originLeft"),i=this.layout._getOption("originTop"),s=t[e?"left":"right"],o=t[i?"top":"bottom"],n=parseFloat(s),r=parseFloat(o),a=this.layout.size;-1!=s.indexOf("%")&&(n=n/100*a.width),-1!=o.indexOf("%")&&(r=r/100*a.height),n=isNaN(n)?0:n,r=isNaN(r)?0:r,n-=e?a.paddingLeft:a.paddingRight,r-=i?a.paddingTop:a.paddingBottom,this.position.x=n,this.position.y=r},l.layoutPosition=function(){var t=this.layout.size,e={},i=this.layout._getOption("originLeft"),s=this.layout._getOption("originTop"),o=i?"paddingLeft":"paddingRight",n=i?"left":"right",r=i?"right":"left",a=this.position.x+t[o];e[n]=this.getXValue(a),e[r]="";var l=s?"paddingTop":"paddingBottom",d=s?"top":"bottom",h=s?"bottom":"top",c=this.position.y+t[l];e[d]=this.getYValue(c),e[h]="",this.css(e),this.emitEvent("layout",[this])},l.getXValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&!e?t/this.layout.size.width*100+"%":t+"px"},l.getYValue=function(t){var e=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&e?t/this.layout.size.height*100+"%":t+"px"},l._transitionTo=function(t,e){this.getPosition();var i=this.position.x,s=this.position.y,o=t==this.position.x&&e==this.position.y;if(this.setPosition(t,e),!o||this.isTransitioning){var n=t-i,r=e-s,a={};a.transform=this.getTranslate(n,r),this.transition({to:a,onTransitionEnd:{transform:this.layoutPosition},isCleaning:!0})}else this.layoutPosition()},l.getTranslate=function(t,e){return"translate3d("+(t=this.layout._getOption("originLeft")?t:-t)+"px, "+(e=this.layout._getOption("originTop")?e:-e)+"px, 0)"},l.goTo=function(t,e){this.setPosition(t,e),this.layoutPosition()},l.moveTo=l._transitionTo,l.setPosition=function(t,e){this.position.x=parseFloat(t),this.position.y=parseFloat(e)},l._nonTransition=function(t){for(var e in this.css(t.to),t.isCleaning&&this._removeStyles(t.to),t.onTransitionEnd)t.onTransitionEnd[e].call(this)},l.transition=function(t){if(parseFloat(this.layout.options.transitionDuration)){var e=this._transn;for(var i in t.onTransitionEnd)e.onEnd[i]=t.onTransitionEnd[i];for(i in t.to)e.ingProperties[i]=!0,t.isCleaning&&(e.clean[i]=!0);if(t.from){this.css(t.from);this.element.offsetHeight;null}this.enableTransition(t.to),this.css(t.to),this.isTransitioning=!0}else this._nonTransition(t)};var d="opacity,"+n.replace(/([A-Z])/g,function(t){return"-"+t.toLowerCase()});l.enableTransition=function(){if(!this.isTransitioning){var t=this.layout.options.transitionDuration;t="number"==typeof t?t+"ms":t,this.css({transitionProperty:d,transitionDuration:t,transitionDelay:this.staggerDelay||0}),this.element.addEventListener(r,this,!1)}},l.onwebkitTransitionEnd=function(t){this.ontransitionend(t)},l.onotransitionend=function(t){this.ontransitionend(t)};var h={"-webkit-transform":"transform"};l.ontransitionend=function(t){if(t.target===this.element){var e=this._transn,i=h[t.propertyName]||t.propertyName;if(delete e.ingProperties[i],function(t){for(var e in t)return!1;return!null}(e.ingProperties)&&this.disableTransition(),i in e.clean&&(this.element.style[t.propertyName]="",delete e.clean[i]),i in e.onEnd)e.onEnd[i].call(this),delete e.onEnd[i];this.emitEvent("transitionEnd",[this])}},l.disableTransition=function(){this.removeTransitionStyles(),this.element.removeEventListener(r,this,!1),this.isTransitioning=!1},l._removeStyles=function(t){var e={};for(var i in t)e[i]="";this.css(e)};var c={transitionProperty:"",transitionDuration:"",transitionDelay:""};return l.removeTransitionStyles=function(){this.css(c)},l.stagger=function(t){t=isNaN(t)?0:t,this.staggerDelay=t+"ms"},l.removeElem=function(){this.element.parentNode.removeChild(this.element),this.css({display:""}),this.emitEvent("remove",[this])},l.remove=function(){return o&&parseFloat(this.layout.options.transitionDuration)?(this.once("transitionEnd",function(){this.removeElem()}),void this.hide()):void this.removeElem()},l.reveal=function(){delete this.isHidden,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("visibleStyle")]=this.onRevealTransitionEnd,this.transition({from:t.hiddenStyle,to:t.visibleStyle,isCleaning:!0,onTransitionEnd:e})},l.onRevealTransitionEnd=function(){this.isHidden||this.emitEvent("reveal")},l.getHideRevealTransitionEndProperty=function(t){var e=this.layout.options[t];if(e.opacity)return"opacity";for(var i in e)return i},l.hide=function(){this.isHidden=!0,this.css({display:""});var t=this.layout.options,e={};e[this.getHideRevealTransitionEndProperty("hiddenStyle")]=this.onHideTransitionEnd,this.transition({from:t.visibleStyle,to:t.hiddenStyle,isCleaning:!0,onTransitionEnd:e})},l.onHideTransitionEnd=function(){this.isHidden&&(this.css({display:"none"}),this.emitEvent("hide"))},l.destroy=function(){this.css({position:"",left:"",right:"",top:"",bottom:"",transition:"",transform:""})},i}),function(o,n){"use strict";"function"==typeof define&&define.amd?define("outlayer/outlayer",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./item"],function(t,e,i,s){return n(o,t,e,i,s)}):"object"==typeof module&&module.exports?module.exports=n(o,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./item")):o.Outlayer=n(o,o.EvEmitter,o.getSize,o.fizzyUIUtils,o.Outlayer.Item)}(window,function(t,e,o,n,s){"use strict";function r(t,e){var i=n.getQueryElement(t);if(i){this.element=i,d&&(this.$element=d(this.element)),this.options=n.extend({},this.constructor.defaults),this.option(e);var s=++h;this.element.outlayerGUID=s,(c[s]=this)._create(),this._getOption("initLayout")&&this.layout()}else l&&l.error("Bad element for "+this.constructor.namespace+": "+(i||t))}function a(t){function e(){t.apply(this,arguments)}return(e.prototype=Object.create(t.prototype)).constructor=e}function i(){}var l=t.console,d=t.jQuery,h=0,c={};r.namespace="outlayer",r.Item=s,r.defaults={containerStyle:{position:"relative"},initLayout:!0,originLeft:!0,originTop:!0,resize:!0,resizeContainer:!0,transitionDuration:"0.4s",hiddenStyle:{opacity:0,transform:"scale(0.001)"},visibleStyle:{opacity:1,transform:"scale(1)"}};var u=r.prototype;n.extend(u,e.prototype),u.option=function(t){n.extend(this.options,t)},u._getOption=function(t){var e=this.constructor.compatOptions[t];return e&&void 0!==this.options[e]?this.options[e]:this.options[t]},r.compatOptions={initLayout:"isInitLayout",horizontal:"isHorizontal",layoutInstant:"isLayoutInstant",originLeft:"isOriginLeft",originTop:"isOriginTop",resize:"isResizeBound",resizeContainer:"isResizingContainer"},u._create=function(){this.reloadItems(),this.stamps=[],this.stamp(this.options.stamp),n.extend(this.element.style,this.options.containerStyle),this._getOption("resize")&&this.bindResize()},u.reloadItems=function(){this.items=this._itemize(this.element.children)},u._itemize=function(t){for(var e=this._filterFindItemElements(t),i=this.constructor.Item,s=[],o=0;o<e.length;o++){var n=new i(e[o],this);s.push(n)}return s},u._filterFindItemElements=function(t){return n.filterFindElements(t,this.options.itemSelector)},u.getItemElements=function(){return this.items.map(function(t){return t.element})},u.layout=function(){this._resetLayout(),this._manageStamps();var t=this._getOption("layoutInstant"),e=void 0!==t?t:!this._isLayoutInited;this.layoutItems(this.items,e),this._isLayoutInited=!0},u._init=u.layout,u._resetLayout=function(){this.getSize()},u.getSize=function(){this.size=o(this.element)},u._getMeasurement=function(t,e){var i,s=this.options[t];s?("string"==typeof s?i=this.element.querySelector(s):s instanceof HTMLElement&&(i=s),this[t]=i?o(i)[e]:s):this[t]=0},u.layoutItems=function(t,e){t=this._getItemsForLayout(t),this._layoutItems(t,e),this._postLayout()},u._getItemsForLayout=function(t){return t.filter(function(t){return!t.isIgnored})},u._layoutItems=function(t,i){if(this._emitCompleteOnItems("layout",t),t&&t.length){var s=[];t.forEach(function(t){var e=this._getItemLayoutPosition(t);e.item=t,e.isInstant=i||t.isLayoutInstant,s.push(e)},this),this._processLayoutQueue(s)}},u._getItemLayoutPosition=function(){return{x:0,y:0}},u._processLayoutQueue=function(t){this.updateStagger(),t.forEach(function(t,e){this._positionItem(t.item,t.x,t.y,t.isInstant,e)},this)},u.updateStagger=function(){var t=this.options.stagger;return null==t?void(this.stagger=0):(this.stagger=function(t){if("number"==typeof t)return t;var e=t.match(/(^\d*\.?\d*)(\w*)/),i=e&&e[1],s=e&&e[2];return i.length?(i=parseFloat(i))*(p[s]||1):0}(t),this.stagger)},u._positionItem=function(t,e,i,s,o){s?t.goTo(e,i):(t.stagger(o*this.stagger),t.moveTo(e,i))},u._postLayout=function(){this.resizeContainer()},u.resizeContainer=function(){if(this._getOption("resizeContainer")){var t=this._getContainerSize();t&&(this._setContainerMeasure(t.width,!0),this._setContainerMeasure(t.height,!1))}},u._getContainerSize=i,u._setContainerMeasure=function(t,e){if(void 0!==t){var i=this.size;i.isBorderBox&&(t+=e?i.paddingLeft+i.paddingRight+i.borderLeftWidth+i.borderRightWidth:i.paddingBottom+i.paddingTop+i.borderTopWidth+i.borderBottomWidth),t=Math.max(t,0),this.element.style[e?"width":"height"]=t+"px"}},u._emitCompleteOnItems=function(e,t){function i(){o.dispatchEvent(e+"Complete",null,[t])}function s(){++r==n&&i()}var o=this,n=t.length;if(t&&n){var r=0;t.forEach(function(t){t.once(e,s)})}else i()},u.dispatchEvent=function(t,e,i){var s=e?[e].concat(i):i;if(this.emitEvent(t,s),d)if(this.$element=this.$element||d(this.element),e){var o=d.Event(e);o.type=t,this.$element.trigger(o,i)}else this.$element.trigger(t,i)},u.ignore=function(t){var e=this.getItem(t);e&&(e.isIgnored=!0)},u.unignore=function(t){var e=this.getItem(t);e&&delete e.isIgnored},u.stamp=function(t){(t=this._find(t))&&(this.stamps=this.stamps.concat(t),t.forEach(this.ignore,this))},u.unstamp=function(t){(t=this._find(t))&&t.forEach(function(t){n.removeFrom(this.stamps,t),this.unignore(t)},this)},u._find=function(t){return t?("string"==typeof t&&(t=this.element.querySelectorAll(t)),t=n.makeArray(t)):void 0},u._manageStamps=function(){this.stamps&&this.stamps.length&&(this._getBoundingRect(),this.stamps.forEach(this._manageStamp,this))},u._getBoundingRect=function(){var t=this.element.getBoundingClientRect(),e=this.size;this._boundingRect={left:t.left+e.paddingLeft+e.borderLeftWidth,top:t.top+e.paddingTop+e.borderTopWidth,right:t.right-(e.paddingRight+e.borderRightWidth),bottom:t.bottom-(e.paddingBottom+e.borderBottomWidth)}},u._manageStamp=i,u._getElementOffset=function(t){var e=t.getBoundingClientRect(),i=this._boundingRect,s=o(t);return{left:e.left-i.left-s.marginLeft,top:e.top-i.top-s.marginTop,right:i.right-e.right-s.marginRight,bottom:i.bottom-e.bottom-s.marginBottom}},u.handleEvent=n.handleEvent,u.bindResize=function(){t.addEventListener("resize",this),this.isResizeBound=!0},u.unbindResize=function(){t.removeEventListener("resize",this),this.isResizeBound=!1},u.onresize=function(){this.resize()},n.debounceMethod(r,"onresize",100),u.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&this.layout()},u.needsResizeLayout=function(){var t=o(this.element);return this.size&&t&&t.innerWidth!==this.size.innerWidth},u.addItems=function(t){var e=this._itemize(t);return e.length&&(this.items=this.items.concat(e)),e},u.appended=function(t){var e=this.addItems(t);e.length&&(this.layoutItems(e,!0),this.reveal(e))},u.prepended=function(t){var e=this._itemize(t);if(e.length){var i=this.items.slice(0);this.items=e.concat(i),this._resetLayout(),this._manageStamps(),this.layoutItems(e,!0),this.reveal(e),this.layoutItems(i)}},u.reveal=function(t){if(this._emitCompleteOnItems("reveal",t),t&&t.length){var i=this.updateStagger();t.forEach(function(t,e){t.stagger(e*i),t.reveal()})}},u.hide=function(t){if(this._emitCompleteOnItems("hide",t),t&&t.length){var i=this.updateStagger();t.forEach(function(t,e){t.stagger(e*i),t.hide()})}},u.revealItemElements=function(t){var e=this.getItems(t);this.reveal(e)},u.hideItemElements=function(t){var e=this.getItems(t);this.hide(e)},u.getItem=function(t){for(var e=0;e<this.items.length;e++){var i=this.items[e];if(i.element==t)return i}},u.getItems=function(t){t=n.makeArray(t);var i=[];return t.forEach(function(t){var e=this.getItem(t);e&&i.push(e)},this),i},u.remove=function(t){var e=this.getItems(t);this._emitCompleteOnItems("remove",e),e&&e.length&&e.forEach(function(t){t.remove(),n.removeFrom(this.items,t)},this)},u.destroy=function(){var t=this.element.style;t.height="",t.position="",t.width="",this.items.forEach(function(t){t.destroy()}),this.unbindResize();var e=this.element.outlayerGUID;delete c[e],delete this.element.outlayerGUID,d&&d.removeData(this.element,this.constructor.namespace)},r.data=function(t){var e=(t=n.getQueryElement(t))&&t.outlayerGUID;return e&&c[e]},r.create=function(t,e){var i=a(r);return i.defaults=n.extend({},r.defaults),n.extend(i.defaults,e),i.compatOptions=n.extend({},r.compatOptions),i.namespace=t,i.data=r.data,i.Item=a(s),n.htmlInit(i,t),d&&d.bridget&&d.bridget(t,i),i};var p={ms:1,s:1e3};return r.Item=s,r}),function(t,e){"function"==typeof define&&define.amd?define(["outlayer/outlayer","get-size/get-size"],e):"object"==typeof module&&module.exports?module.exports=e(require("outlayer"),require("get-size")):t.Masonry=e(t.Outlayer,t.getSize)}(window,function(t,d){var e=t.create("masonry");e.compatOptions.fitWidth="isFitWidth";var i=e.prototype;return i._resetLayout=function(){this.getSize(),this._getMeasurement("columnWidth","outerWidth"),this._getMeasurement("gutter","outerWidth"),this.measureColumns(),this.colYs=[];for(var t=0;t<this.cols;t++)this.colYs.push(0);this.maxY=0,this.horizontalColIndex=0},i.measureColumns=function(){if(this.getContainerWidth(),!this.columnWidth){var t=this.items[0],e=t&&t.element;this.columnWidth=e&&d(e).outerWidth||this.containerWidth}var i=this.columnWidth+=this.gutter,s=this.containerWidth+this.gutter,o=s/i,n=i-s%i;o=Math[n&&n<1?"round":"floor"](o),this.cols=Math.max(o,1)},i.getContainerWidth=function(){var t=this._getOption("fitWidth")?this.element.parentNode:this.element,e=d(t);this.containerWidth=e&&e.innerWidth},i._getItemLayoutPosition=function(t){t.getSize();var e=t.size.outerWidth%this.columnWidth,i=Math[e&&e<1?"round":"ceil"](t.size.outerWidth/this.columnWidth);i=Math.min(i,this.cols);for(var s=this[this.options.horizontalOrder?"_getHorizontalColPosition":"_getTopColPosition"](i,t),o={x:this.columnWidth*s.col,y:s.y},n=s.y+t.size.outerHeight,r=i+s.col,a=s.col;a<r;a++)this.colYs[a]=n;return o},i._getTopColPosition=function(t){var e=this._getTopColGroup(t),i=Math.min.apply(Math,e);return{col:e.indexOf(i),y:i}},i._getTopColGroup=function(t){if(t<2)return this.colYs;for(var e=[],i=this.cols+1-t,s=0;s<i;s++)e[s]=this._getColGroupY(s,t);return e},i._getColGroupY=function(t,e){if(e<2)return this.colYs[t];var i=this.colYs.slice(t,t+e);return Math.max.apply(Math,i)},i._getHorizontalColPosition=function(t,e){var i=this.horizontalColIndex%this.cols;i=1<t&&i+t>this.cols?0:i;var s=e.size.outerWidth&&e.size.outerHeight;return this.horizontalColIndex=s?i+t:this.horizontalColIndex,{col:i,y:this._getColGroupY(i,t)}},i._manageStamp=function(t){var e=d(t),i=this._getElementOffset(t),s=this._getOption("originLeft")?i.left:i.right,o=s+e.outerWidth,n=Math.floor(s/this.columnWidth);n=Math.max(0,n);var r=Math.floor(o/this.columnWidth);r-=o%this.columnWidth?0:1,r=Math.min(this.cols-1,r);for(var a=(this._getOption("originTop")?i.top:i.bottom)+e.outerHeight,l=n;l<=r;l++)this.colYs[l]=Math.max(a,this.colYs[l])},i._getContainerSize=function(){this.maxY=Math.max.apply(Math,this.colYs);var t={height:this.maxY};return this._getOption("fitWidth")&&(t.width=this._getContainerFitWidth()),t},i._getContainerFitWidth=function(){for(var t=0,e=this.cols;--e&&0===this.colYs[e];)t++;return(this.cols-t)*this.columnWidth-this.gutter},i.needsResizeLayout=function(){var t=this.containerWidth;return this.getContainerWidth(),t!=this.containerWidth},e}),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"undefined"!=typeof exports?module.exports=t(require("jquery")):t(jQuery)}(function(d){"use strict";var o,r=window.Slick||{};o=0,(r=function(t,e){var i,s=this;s.defaults={accessibility:!0,adaptiveHeight:!1,appendArrows:d(t),appendDots:d(t),arrows:!0,asNavFor:null,prevArrow:'<button class="slick-prev" aria-label="Previous" type="button">Previous</button>',nextArrow:'<button class="slick-next" aria-label="Next" type="button">Next</button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:"50px",cssEase:"ease",customPaging:function(t,e){return d('<button type="button" />').text(e+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,focusOnChange:!1,infinite:!0,initialSlide:0,lazyLoad:"ondemand",mobileFirst:!1,pauseOnHover:!0,pauseOnFocus:!0,pauseOnDotsHover:!1,respondTo:"window",responsive:null,rows:1,rtl:!1,slide:"",slidesPerRow:1,slidesToShow:1,slidesToScroll:1,speed:500,swipe:!0,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,verticalSwiping:!1,waitForAnimate:!0,zIndex:1e3},s.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,scrolling:!1,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,swiping:!1,$list:null,touchObject:{},transformsEnabled:!1,unslicked:!1},d.extend(s,s.initials),s.activeBreakpoint=null,s.animType=null,s.animProp=null,s.breakpoints=[],s.breakpointSettings=[],s.cssTransitions=!1,s.focussed=!1,s.interrupted=!1,s.hidden="hidden",s.paused=!0,s.positionProp=null,s.respondTo=null,s.rowCount=1,s.shouldClick=!0,s.$slider=d(t),s.$slidesCache=null,s.transformType=null,s.transitionType=null,s.visibilityChange="visibilitychange",s.windowWidth=0,s.windowTimer=null,i=d(t).data("slick")||{},s.options=d.extend({},s.defaults,e,i),s.currentSlide=s.options.initialSlide,s.originalSettings=s.options,void 0!==document.mozHidden?(s.hidden="mozHidden",s.visibilityChange="mozvisibilitychange"):void 0!==document.webkitHidden&&(s.hidden="webkitHidden",s.visibilityChange="webkitvisibilitychange"),s.autoPlay=d.proxy(s.autoPlay,s),s.autoPlayClear=d.proxy(s.autoPlayClear,s),s.autoPlayIterator=d.proxy(s.autoPlayIterator,s),s.changeSlide=d.proxy(s.changeSlide,s),s.clickHandler=d.proxy(s.clickHandler,s),s.selectHandler=d.proxy(s.selectHandler,s),s.setPosition=d.proxy(s.setPosition,s),s.swipeHandler=d.proxy(s.swipeHandler,s),s.dragHandler=d.proxy(s.dragHandler,s),s.keyHandler=d.proxy(s.keyHandler,s),s.instanceUid=o++,s.htmlExpr=/^(?:\s*(<[\w\W]+>)[^>]*)$/,s.registerBreakpoints(),s.init(!0)}).prototype.activateADA=function(){this.$slideTrack.find(".slick-active").attr({"aria-hidden":"false"}).find("a, input, button, select").attr({tabindex:"0"})},r.prototype.addSlide=r.prototype.slickAdd=function(t,e,i){var s=this;if("boolean"==typeof e)i=e,e=null;else if(e<0||e>=s.slideCount)return!1;s.unload(),"number"==typeof e?0===e&&0===s.$slides.length?d(t).appendTo(s.$slideTrack):i?d(t).insertBefore(s.$slides.eq(e)):d(t).insertAfter(s.$slides.eq(e)):!0===i?d(t).prependTo(s.$slideTrack):d(t).appendTo(s.$slideTrack),s.$slides=s.$slideTrack.children(this.options.slide),s.$slideTrack.children(this.options.slide).detach(),s.$slideTrack.append(s.$slides),s.$slides.each(function(t,e){d(e).attr("data-slick-index",t)}),s.$slidesCache=s.$slides,s.reinit()},r.prototype.animateHeight=function(){var t=this;if(1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical){var e=t.$slides.eq(t.currentSlide).outerHeight(!0);t.$list.animate({height:e},t.options.speed)}},r.prototype.animateSlide=function(t,e){var i={},s=this;s.animateHeight(),!0===s.options.rtl&&!1===s.options.vertical&&(t=-t),!1===s.transformsEnabled?!1===s.options.vertical?s.$slideTrack.animate({left:t},s.options.speed,s.options.easing,e):s.$slideTrack.animate({top:t},s.options.speed,s.options.easing,e):!1===s.cssTransitions?(!0===s.options.rtl&&(s.currentLeft=-s.currentLeft),d({animStart:s.currentLeft}).animate({animStart:t},{duration:s.options.speed,easing:s.options.easing,step:function(t){t=Math.ceil(t),!1===s.options.vertical?i[s.animType]="translate("+t+"px, 0px)":i[s.animType]="translate(0px,"+t+"px)",s.$slideTrack.css(i)},complete:function(){e&&e.call()}})):(s.applyTransition(),t=Math.ceil(t),!1===s.options.vertical?i[s.animType]="translate3d("+t+"px, 0px, 0px)":i[s.animType]="translate3d(0px,"+t+"px, 0px)",s.$slideTrack.css(i),e&&setTimeout(function(){s.disableTransition(),e.call()},s.options.speed))},r.prototype.getNavTarget=function(){var t=this.options.asNavFor;return t&&null!==t&&(t=d(t).not(this.$slider)),t},r.prototype.asNavFor=function(e){var t=this.getNavTarget();null!==t&&"object"==typeof t&&t.each(function(){var t=d(this).slick("getSlick");t.unslicked||t.slideHandler(e,!0)})},r.prototype.applyTransition=function(t){var e=this,i={};!1===e.options.fade?i[e.transitionType]=e.transformType+" "+e.options.speed+"ms "+e.options.cssEase:i[e.transitionType]="opacity "+e.options.speed+"ms "+e.options.cssEase,!1===e.options.fade?e.$slideTrack.css(i):e.$slides.eq(t).css(i)},r.prototype.autoPlay=function(){var t=this;t.autoPlayClear(),t.slideCount>t.options.slidesToShow&&(t.autoPlayTimer=setInterval(t.autoPlayIterator,t.options.autoplaySpeed))},r.prototype.autoPlayClear=function(){this.autoPlayTimer&&clearInterval(this.autoPlayTimer)},r.prototype.autoPlayIterator=function(){var t=this,e=t.currentSlide+t.options.slidesToScroll;t.paused||t.interrupted||t.focussed||(!1===t.options.infinite&&(1===t.direction&&t.currentSlide+1===t.slideCount-1?t.direction=0:0===t.direction&&(e=t.currentSlide-t.options.slidesToScroll,t.currentSlide-1==0&&(t.direction=1))),t.slideHandler(e))},r.prototype.buildArrows=function(){var t=this;!0===t.options.arrows&&(t.$prevArrow=d(t.options.prevArrow).addClass("slick-arrow"),t.$nextArrow=d(t.options.nextArrow).addClass("slick-arrow"),t.slideCount>t.options.slidesToShow?(t.$prevArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),t.$nextArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.prependTo(t.options.appendArrows),t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.appendTo(t.options.appendArrows),!0!==t.options.infinite&&t.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true")):t.$prevArrow.add(t.$nextArrow).addClass("slick-hidden").attr({"aria-disabled":"true",tabindex:"-1"}))},r.prototype.buildDots=function(){var t,e,i=this;if(!0===i.options.dots&&i.slideCount>i.options.slidesToShow){for(i.$slider.addClass("slick-dotted"),e=d("<ul />").addClass(i.options.dotsClass),t=0;t<=i.getDotCount();t+=1)e.append(d("<li />").append(i.options.customPaging.call(this,i,t)));i.$dots=e.appendTo(i.options.appendDots),i.$dots.find("li").first().addClass("slick-active")}},r.prototype.buildOut=function(){var t=this;t.$slides=t.$slider.children(t.options.slide+":not(.slick-cloned)").addClass("slick-slide"),t.slideCount=t.$slides.length,t.$slides.each(function(t,e){d(e).attr("data-slick-index",t).data("originalStyling",d(e).attr("style")||"")}),t.$slider.addClass("slick-slider"),t.$slideTrack=0===t.slideCount?d('<div class="slick-track"/>').appendTo(t.$slider):t.$slides.wrapAll('<div class="slick-track"/>').parent(),t.$list=t.$slideTrack.wrap('<div class="slick-list"/>').parent(),t.$slideTrack.css("opacity",0),!0!==t.options.centerMode&&!0!==t.options.swipeToSlide||(t.options.slidesToScroll=1),d("img[data-lazy]",t.$slider).not("[src]").addClass("slick-loading"),t.setupInfinite(),t.buildArrows(),t.buildDots(),t.updateDots(),t.setSlideClasses("number"==typeof t.currentSlide?t.currentSlide:0),!0===t.options.draggable&&t.$list.addClass("draggable")},r.prototype.buildRows=function(){var t,e,i,s,o,n,r,a=this;if(s=document.createDocumentFragment(),n=a.$slider.children(),0<a.options.rows){for(r=a.options.slidesPerRow*a.options.rows,o=Math.ceil(n.length/r),t=0;t<o;t++){var l=document.createElement("div");for(e=0;e<a.options.rows;e++){var d=document.createElement("div");for(i=0;i<a.options.slidesPerRow;i++){var h=t*r+(e*a.options.slidesPerRow+i);n.get(h)&&d.appendChild(n.get(h))}l.appendChild(d)}s.appendChild(l)}a.$slider.empty().append(s),a.$slider.children().children().children().css({width:100/a.options.slidesPerRow+"%",display:"inline-block"})}},r.prototype.checkResponsive=function(t,e){var i,s,o,n=this,r=!1,a=n.$slider.width(),l=window.innerWidth||d(window).width();if("window"===n.respondTo?o=l:"slider"===n.respondTo?o=a:"min"===n.respondTo&&(o=Math.min(l,a)),n.options.responsive&&n.options.responsive.length&&null!==n.options.responsive){for(i in s=null,n.breakpoints)n.breakpoints.hasOwnProperty(i)&&(!1===n.originalSettings.mobileFirst?o<n.breakpoints[i]&&(s=n.breakpoints[i]):o>n.breakpoints[i]&&(s=n.breakpoints[i]));null!==s?null!==n.activeBreakpoint&&s===n.activeBreakpoint&&!e||(n.activeBreakpoint=s,"unslick"===n.breakpointSettings[s]?n.unslick(s):(n.options=d.extend({},n.originalSettings,n.breakpointSettings[s]),!0===t&&(n.currentSlide=n.options.initialSlide),n.refresh(t)),r=s):null!==n.activeBreakpoint&&(n.activeBreakpoint=null,n.options=n.originalSettings,!0===t&&(n.currentSlide=n.options.initialSlide),n.refresh(t),r=s),t||!1===r||n.$slider.trigger("breakpoint",[n,r])}},r.prototype.changeSlide=function(t,e){var i,s,o=this,n=d(t.currentTarget);switch(n.is("a")&&t.preventDefault(),n.is("li")||(n=n.closest("li")),i=o.slideCount%o.options.slidesToScroll!=0?0:(o.slideCount-o.currentSlide)%o.options.slidesToScroll,t.data.message){case"previous":s=0==i?o.options.slidesToScroll:o.options.slidesToShow-i,o.slideCount>o.options.slidesToShow&&o.slideHandler(o.currentSlide-s,!1,e);break;case"next":s=0==i?o.options.slidesToScroll:i,o.slideCount>o.options.slidesToShow&&o.slideHandler(o.currentSlide+s,!1,e);break;case"index":var r=0===t.data.index?0:t.data.index||n.index()*o.options.slidesToScroll;o.slideHandler(o.checkNavigable(r),!1,e),n.children().trigger("focus");break;default:return}},r.prototype.checkNavigable=function(t){var e,i;if(i=0,t>(e=this.getNavigableIndexes())[e.length-1])t=e[e.length-1];else for(var s in e){if(t<e[s]){t=i;break}i=e[s]}return t},r.prototype.cleanUpEvents=function(){var t=this;t.options.dots&&null!==t.$dots&&(d("li",t.$dots).off("click.slick",t.changeSlide).off("mouseenter.slick",d.proxy(t.interrupt,t,!0)).off("mouseleave.slick",d.proxy(t.interrupt,t,!1)),!0===t.options.accessibility&&t.$dots.off("keydown.slick",t.keyHandler)),t.$slider.off("focus.slick blur.slick"),!0===t.options.arrows&&t.slideCount>t.options.slidesToShow&&(t.$prevArrow&&t.$prevArrow.off("click.slick",t.changeSlide),t.$nextArrow&&t.$nextArrow.off("click.slick",t.changeSlide),!0===t.options.accessibility&&(t.$prevArrow&&t.$prevArrow.off("keydown.slick",t.keyHandler),t.$nextArrow&&t.$nextArrow.off("keydown.slick",t.keyHandler))),t.$list.off("touchstart.slick mousedown.slick",t.swipeHandler),t.$list.off("touchmove.slick mousemove.slick",t.swipeHandler),t.$list.off("touchend.slick mouseup.slick",t.swipeHandler),t.$list.off("touchcancel.slick mouseleave.slick",t.swipeHandler),t.$list.off("click.slick",t.clickHandler),d(document).off(t.visibilityChange,t.visibility),t.cleanUpSlideEvents(),!0===t.options.accessibility&&t.$list.off("keydown.slick",t.keyHandler),!0===t.options.focusOnSelect&&d(t.$slideTrack).children().off("click.slick",t.selectHandler),d(window).off("orientationchange.slick.slick-"+t.instanceUid,t.orientationChange),d(window).off("resize.slick.slick-"+t.instanceUid,t.resize),d("[draggable!=true]",t.$slideTrack).off("dragstart",t.preventDefault),d(window).off("load.slick.slick-"+t.instanceUid,t.setPosition)},r.prototype.cleanUpSlideEvents=function(){var t=this;t.$list.off("mouseenter.slick",d.proxy(t.interrupt,t,!0)),t.$list.off("mouseleave.slick",d.proxy(t.interrupt,t,!1))},r.prototype.cleanUpRows=function(){var t;0<this.options.rows&&((t=this.$slides.children().children()).removeAttr("style"),this.$slider.empty().append(t))},r.prototype.clickHandler=function(t){!1===this.shouldClick&&(t.stopImmediatePropagation(),t.stopPropagation(),t.preventDefault())},r.prototype.destroy=function(t){var e=this;e.autoPlayClear(),e.touchObject={},e.cleanUpEvents(),d(".slick-cloned",e.$slider).detach(),e.$dots&&e.$dots.remove(),e.$prevArrow&&e.$prevArrow.length&&(e.$prevArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.remove()),e.$nextArrow&&e.$nextArrow.length&&(e.$nextArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.remove()),e.$slides&&(e.$slides.removeClass("slick-slide slick-active slick-center slick-visible slick-current").removeAttr("aria-hidden").removeAttr("data-slick-index").each(function(){d(this).attr("style",d(this).data("originalStyling"))}),e.$slideTrack.children(this.options.slide).detach(),e.$slideTrack.detach(),e.$list.detach(),e.$slider.append(e.$slides)),e.cleanUpRows(),e.$slider.removeClass("slick-slider"),e.$slider.removeClass("slick-initialized"),e.$slider.removeClass("slick-dotted"),e.unslicked=!0,t||e.$slider.trigger("destroy",[e])},r.prototype.disableTransition=function(t){var e={};e[this.transitionType]="",!1===this.options.fade?this.$slideTrack.css(e):this.$slides.eq(t).css(e)},r.prototype.fadeSlide=function(t,e){var i=this;!1===i.cssTransitions?(i.$slides.eq(t).css({zIndex:i.options.zIndex}),i.$slides.eq(t).animate({opacity:1},i.options.speed,i.options.easing,e)):(i.applyTransition(t),i.$slides.eq(t).css({opacity:1,zIndex:i.options.zIndex}),e&&setTimeout(function(){i.disableTransition(t),e.call()},i.options.speed))},r.prototype.fadeSlideOut=function(t){var e=this;!1===e.cssTransitions?e.$slides.eq(t).animate({opacity:0,zIndex:e.options.zIndex-2},e.options.speed,e.options.easing):(e.applyTransition(t),e.$slides.eq(t).css({opacity:0,zIndex:e.options.zIndex-2}))},r.prototype.filterSlides=r.prototype.slickFilter=function(t){var e=this;null!==t&&(e.$slidesCache=e.$slides,e.unload(),e.$slideTrack.children(this.options.slide).detach(),e.$slidesCache.filter(t).appendTo(e.$slideTrack),e.reinit())},r.prototype.focusHandler=function(){var i=this;i.$slider.off("focus.slick blur.slick").on("focus.slick","*",function(t){var e=d(this);setTimeout(function(){i.options.pauseOnFocus&&e.is(":focus")&&(i.focussed=!0,i.autoPlay())},0)}).on("blur.slick","*",function(t){d(this);i.options.pauseOnFocus&&(i.focussed=!1,i.autoPlay())})},r.prototype.getCurrent=r.prototype.slickCurrentSlide=function(){return this.currentSlide},r.prototype.getDotCount=function(){var t=this,e=0,i=0,s=0;if(!0===t.options.infinite)if(t.slideCount<=t.options.slidesToShow)++s;else for(;e<t.slideCount;)++s,e=i+t.options.slidesToScroll,i+=t.options.slidesToScroll<=t.options.slidesToShow?t.options.slidesToScroll:t.options.slidesToShow;else if(!0===t.options.centerMode)s=t.slideCount;else if(t.options.asNavFor)for(;e<t.slideCount;)++s,e=i+t.options.slidesToScroll,i+=t.options.slidesToScroll<=t.options.slidesToShow?t.options.slidesToScroll:t.options.slidesToShow;else s=1+Math.ceil((t.slideCount-t.options.slidesToShow)/t.options.slidesToScroll);return s-1},r.prototype.getLeft=function(t){var e,i,s,o,n=this,r=0;return n.slideOffset=0,i=n.$slides.first().outerHeight(!0),!0===n.options.infinite?(n.slideCount>n.options.slidesToShow&&(n.slideOffset=n.slideWidth*n.options.slidesToShow*-1,o=-1,!0===n.options.vertical&&!0===n.options.centerMode&&(2===n.options.slidesToShow?o=-1.5:1===n.options.slidesToShow&&(o=-2)),r=i*n.options.slidesToShow*o),n.slideCount%n.options.slidesToScroll!=0&&t+n.options.slidesToScroll>n.slideCount&&n.slideCount>n.options.slidesToShow&&(r=t>n.slideCount?(n.slideOffset=(n.options.slidesToShow-(t-n.slideCount))*n.slideWidth*-1,(n.options.slidesToShow-(t-n.slideCount))*i*-1):(n.slideOffset=n.slideCount%n.options.slidesToScroll*n.slideWidth*-1,n.slideCount%n.options.slidesToScroll*i*-1))):t+n.options.slidesToShow>n.slideCount&&(n.slideOffset=(t+n.options.slidesToShow-n.slideCount)*n.slideWidth,r=(t+n.options.slidesToShow-n.slideCount)*i),n.slideCount<=n.options.slidesToShow&&(r=n.slideOffset=0),!0===n.options.centerMode&&n.slideCount<=n.options.slidesToShow?n.slideOffset=n.slideWidth*Math.floor(n.options.slidesToShow)/2-n.slideWidth*n.slideCount/2:!0===n.options.centerMode&&!0===n.options.infinite?n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)-n.slideWidth:!0===n.options.centerMode&&(n.slideOffset=0,n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)),e=!1===n.options.vertical?t*n.slideWidth*-1+n.slideOffset:t*i*-1+r,!0===n.options.variableWidth&&(s=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(t):n.$slideTrack.children(".slick-slide").eq(t+n.options.slidesToShow),e=!0===n.options.rtl?s[0]?-1*(n.$slideTrack.width()-s[0].offsetLeft-s.width()):0:s[0]?-1*s[0].offsetLeft:0,!0===n.options.centerMode&&(s=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(t):n.$slideTrack.children(".slick-slide").eq(t+n.options.slidesToShow+1),e=!0===n.options.rtl?s[0]?-1*(n.$slideTrack.width()-s[0].offsetLeft-s.width()):0:s[0]?-1*s[0].offsetLeft:0,e+=(n.$list.width()-s.outerWidth())/2)),e},r.prototype.getOption=r.prototype.slickGetOption=function(t){return this.options[t]},r.prototype.getNavigableIndexes=function(){var t,e=this,i=0,s=0,o=[];for(t=!1===e.options.infinite?e.slideCount:(i=-1*e.options.slidesToScroll,s=-1*e.options.slidesToScroll,2*e.slideCount);i<t;)o.push(i),i=s+e.options.slidesToScroll,s+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;return o},r.prototype.getSlick=function(){return this},r.prototype.getSlideCount=function(){var o,n,t,r=this;return t=!0===r.options.centerMode?Math.floor(r.$list.width()/2):0,n=-1*r.swipeLeft+t,!0===r.options.swipeToSlide?(r.$slideTrack.find(".slick-slide").each(function(t,e){var i,s;if(i=d(e).outerWidth(),s=e.offsetLeft,!0!==r.options.centerMode&&(s+=i/2),n<s+i)return o=e,!1}),Math.abs(d(o).attr("data-slick-index")-r.currentSlide)||1):r.options.slidesToScroll},r.prototype.goTo=r.prototype.slickGoTo=function(t,e){this.changeSlide({data:{message:"index",index:parseInt(t)}},e)},r.prototype.init=function(t){var e=this;d(e.$slider).hasClass("slick-initialized")||(d(e.$slider).addClass("slick-initialized"),e.buildRows(),e.buildOut(),e.setProps(),e.startLoad(),e.loadSlider(),e.initializeEvents(),e.updateArrows(),e.updateDots(),e.checkResponsive(!0),e.focusHandler()),t&&e.$slider.trigger("init",[e]),!0===e.options.accessibility&&e.initADA(),e.options.autoplay&&(e.paused=!1,e.autoPlay())},r.prototype.initADA=function(){var s=this,i=Math.ceil(s.slideCount/s.options.slidesToShow),o=s.getNavigableIndexes().filter(function(t){return 0<=t&&t<s.slideCount});s.$slides.add(s.$slideTrack.find(".slick-cloned")).attr({"aria-hidden":"true",tabindex:"-1"}).find("a, input, button, select").attr({tabindex:"-1"}),null!==s.$dots&&(s.$slides.not(s.$slideTrack.find(".slick-cloned")).each(function(t){var e=o.indexOf(t);if(d(this).attr({role:"tabpanel",id:"slick-slide"+s.instanceUid+t,tabindex:-1}),-1!==e){var i="slick-slide-control"+s.instanceUid+e;d("#"+i).length&&d(this).attr({"aria-describedby":i})}}),s.$dots.attr("role","tablist").find("li").each(function(t){var e=o[t];d(this).attr({role:"presentation"}),d(this).find("button").first().attr({role:"tab",id:"slick-slide-control"+s.instanceUid+t,"aria-controls":"slick-slide"+s.instanceUid+e,"aria-label":t+1+" of "+i,"aria-selected":null,tabindex:"-1"})}).eq(s.currentSlide).find("button").attr({"aria-selected":"true",tabindex:"0"}).end());for(var t=s.currentSlide,e=t+s.options.slidesToShow;t<e;t++)s.options.focusOnChange?s.$slides.eq(t).attr({tabindex:"0"}):s.$slides.eq(t).removeAttr("tabindex");s.activateADA()},r.prototype.initArrowEvents=function(){var t=this;!0===t.options.arrows&&t.slideCount>t.options.slidesToShow&&(t.$prevArrow.off("click.slick").on("click.slick",{message:"previous"},t.changeSlide),t.$nextArrow.off("click.slick").on("click.slick",{message:"next"},t.changeSlide),!0===t.options.accessibility&&(t.$prevArrow.on("keydown.slick",t.keyHandler),t.$nextArrow.on("keydown.slick",t.keyHandler)))},r.prototype.initDotEvents=function(){var t=this;!0===t.options.dots&&t.slideCount>t.options.slidesToShow&&(d("li",t.$dots).on("click.slick",{message:"index"},t.changeSlide),!0===t.options.accessibility&&t.$dots.on("keydown.slick",t.keyHandler)),!0===t.options.dots&&!0===t.options.pauseOnDotsHover&&t.slideCount>t.options.slidesToShow&&d("li",t.$dots).on("mouseenter.slick",d.proxy(t.interrupt,t,!0)).on("mouseleave.slick",d.proxy(t.interrupt,t,!1))},r.prototype.initSlideEvents=function(){var t=this;t.options.pauseOnHover&&(t.$list.on("mouseenter.slick",d.proxy(t.interrupt,t,!0)),t.$list.on("mouseleave.slick",d.proxy(t.interrupt,t,!1)))},r.prototype.initializeEvents=function(){var t=this;t.initArrowEvents(),t.initDotEvents(),t.initSlideEvents(),t.$list.on("touchstart.slick mousedown.slick",{action:"start"},t.swipeHandler),t.$list.on("touchmove.slick mousemove.slick",{action:"move"},t.swipeHandler),t.$list.on("touchend.slick mouseup.slick",{action:"end"},t.swipeHandler),t.$list.on("touchcancel.slick mouseleave.slick",{action:"end"},t.swipeHandler),t.$list.on("click.slick",t.clickHandler),d(document).on(t.visibilityChange,d.proxy(t.visibility,t)),!0===t.options.accessibility&&t.$list.on("keydown.slick",t.keyHandler),!0===t.options.focusOnSelect&&d(t.$slideTrack).children().on("click.slick",t.selectHandler),d(window).on("orientationchange.slick.slick-"+t.instanceUid,d.proxy(t.orientationChange,t)),d(window).on("resize.slick.slick-"+t.instanceUid,d.proxy(t.resize,t)),d("[draggable!=true]",t.$slideTrack).on("dragstart",t.preventDefault),d(window).on("load.slick.slick-"+t.instanceUid,t.setPosition),d(t.setPosition)},r.prototype.initUI=function(){var t=this;!0===t.options.arrows&&t.slideCount>t.options.slidesToShow&&(t.$prevArrow.show(),t.$nextArrow.show()),!0===t.options.dots&&t.slideCount>t.options.slidesToShow&&t.$dots.show()},r.prototype.keyHandler=function(t){var e=this;t.target.tagName.match("TEXTAREA|INPUT|SELECT")||(37===t.keyCode&&!0===e.options.accessibility?e.changeSlide({data:{message:!0===e.options.rtl?"next":"previous"}}):39===t.keyCode&&!0===e.options.accessibility&&e.changeSlide({data:{message:!0===e.options.rtl?"previous":"next"}}))},r.prototype.lazyLoad=function(){var t,e,i,n=this;function s(t){d("img[data-lazy]",t).each(function(){var t=d(this),e=d(this).attr("data-lazy"),i=d(this).attr("data-srcset"),s=d(this).attr("data-sizes")||n.$slider.attr("data-sizes"),o=document.createElement("img");o.onload=function(){t.animate({opacity:0},100,function(){i&&(t.attr("srcset",i),s&&t.attr("sizes",s)),t.attr("src",e).animate({opacity:1},200,function(){t.removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading")}),n.$slider.trigger("lazyLoaded",[n,t,e])})},o.onerror=function(){t.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),n.$slider.trigger("lazyLoadError",[n,t,e])},o.src=e})}if(!0===n.options.centerMode?i=!0===n.options.infinite?(e=n.currentSlide+(n.options.slidesToShow/2+1))+n.options.slidesToShow+2:(e=Math.max(0,n.currentSlide-(n.options.slidesToShow/2+1)),n.options.slidesToShow/2+1+2+n.currentSlide):(e=n.options.infinite?n.options.slidesToShow+n.currentSlide:n.currentSlide,i=Math.ceil(e+n.options.slidesToShow),!0===n.options.fade&&(0<e&&e--,i<=n.slideCount&&i++)),t=n.$slider.find(".slick-slide").slice(e,i),"anticipated"===n.options.lazyLoad)for(var o=e-1,r=i,a=n.$slider.find(".slick-slide"),l=0;l<n.options.slidesToScroll;l++)o<0&&(o=n.slideCount-1),t=(t=t.add(a.eq(o))).add(a.eq(r)),o--,r++;s(t),n.slideCount<=n.options.slidesToShow?s(n.$slider.find(".slick-slide")):n.currentSlide>=n.slideCount-n.options.slidesToShow?s(n.$slider.find(".slick-cloned").slice(0,n.options.slidesToShow)):0===n.currentSlide&&s(n.$slider.find(".slick-cloned").slice(-1*n.options.slidesToShow))},r.prototype.loadSlider=function(){var t=this;t.setPosition(),t.$slideTrack.css({opacity:1}),t.$slider.removeClass("slick-loading"),t.initUI(),"progressive"===t.options.lazyLoad&&t.progressiveLazyLoad()},r.prototype.next=r.prototype.slickNext=function(){this.changeSlide({data:{message:"next"}})},r.prototype.orientationChange=function(){this.checkResponsive(),this.setPosition()},r.prototype.pause=r.prototype.slickPause=function(){this.autoPlayClear(),this.paused=!0},r.prototype.play=r.prototype.slickPlay=function(){var t=this;t.autoPlay(),t.options.autoplay=!0,t.paused=!1,t.focussed=!1,t.interrupted=!1},r.prototype.postSlide=function(t){var e=this;e.unslicked||(e.$slider.trigger("afterChange",[e,t]),e.animating=!1,e.slideCount>e.options.slidesToShow&&e.setPosition(),e.swipeLeft=null,e.options.autoplay&&e.autoPlay(),!0===e.options.accessibility&&(e.initADA(),e.options.focusOnChange&&d(e.$slides.get(e.currentSlide)).attr("tabindex",0).focus()))},r.prototype.prev=r.prototype.slickPrev=function(){this.changeSlide({data:{message:"previous"}})},r.prototype.preventDefault=function(t){t.preventDefault()},r.prototype.progressiveLazyLoad=function(t){t=t||1;var e,i,s,o,n,r=this,a=d("img[data-lazy]",r.$slider);a.length?(e=a.first(),i=e.attr("data-lazy"),s=e.attr("data-srcset"),o=e.attr("data-sizes")||r.$slider.attr("data-sizes"),(n=document.createElement("img")).onload=function(){s&&(e.attr("srcset",s),o&&e.attr("sizes",o)),e.attr("src",i).removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading"),!0===r.options.adaptiveHeight&&r.setPosition(),r.$slider.trigger("lazyLoaded",[r,e,i]),r.progressiveLazyLoad()},n.onerror=function(){t<3?setTimeout(function(){r.progressiveLazyLoad(t+1)},500):(e.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),r.$slider.trigger("lazyLoadError",[r,e,i]),r.progressiveLazyLoad())},n.src=i):r.$slider.trigger("allImagesLoaded",[r])},r.prototype.refresh=function(t){var e,i,s=this;i=s.slideCount-s.options.slidesToShow,!s.options.infinite&&s.currentSlide>i&&(s.currentSlide=i),s.slideCount<=s.options.slidesToShow&&(s.currentSlide=0),e=s.currentSlide,s.destroy(!0),d.extend(s,s.initials,{currentSlide:e}),s.init(),t||s.changeSlide({data:{message:"index",index:e}},!1)},r.prototype.registerBreakpoints=function(){var t,e,i,s=this,o=s.options.responsive||null;if("array"===d.type(o)&&o.length){for(t in s.respondTo=s.options.respondTo||"window",o)if(i=s.breakpoints.length-1,o.hasOwnProperty(t)){for(e=o[t].breakpoint;0<=i;)s.breakpoints[i]&&s.breakpoints[i]===e&&s.breakpoints.splice(i,1),i--;s.breakpoints.push(e),s.breakpointSettings[e]=o[t].settings}s.breakpoints.sort(function(t,e){return s.options.mobileFirst?t-e:e-t})}},r.prototype.reinit=function(){var t=this;t.$slides=t.$slideTrack.children(t.options.slide).addClass("slick-slide"),t.slideCount=t.$slides.length,t.currentSlide>=t.slideCount&&0!==t.currentSlide&&(t.currentSlide=t.currentSlide-t.options.slidesToScroll),t.slideCount<=t.options.slidesToShow&&(t.currentSlide=0),t.registerBreakpoints(),t.setProps(),t.setupInfinite(),t.buildArrows(),t.updateArrows(),t.initArrowEvents(),t.buildDots(),t.updateDots(),t.initDotEvents(),t.cleanUpSlideEvents(),t.initSlideEvents(),t.checkResponsive(!1,!0),!0===t.options.focusOnSelect&&d(t.$slideTrack).children().on("click.slick",t.selectHandler),t.setSlideClasses("number"==typeof t.currentSlide?t.currentSlide:0),t.setPosition(),t.focusHandler(),t.paused=!t.options.autoplay,t.autoPlay(),t.$slider.trigger("reInit",[t])},r.prototype.resize=function(){var t=this;d(window).width()!==t.windowWidth&&(clearTimeout(t.windowDelay),t.windowDelay=window.setTimeout(function(){t.windowWidth=d(window).width(),t.checkResponsive(),t.unslicked||t.setPosition()},50))},r.prototype.removeSlide=r.prototype.slickRemove=function(t,e,i){var s=this;if(t="boolean"==typeof t?!0===(e=t)?0:s.slideCount-1:!0===e?--t:t,s.slideCount<1||t<0||t>s.slideCount-1)return!1;s.unload(),!0===i?s.$slideTrack.children().remove():s.$slideTrack.children(this.options.slide).eq(t).remove(),s.$slides=s.$slideTrack.children(this.options.slide),s.$slideTrack.children(this.options.slide).detach(),s.$slideTrack.append(s.$slides),s.$slidesCache=s.$slides,s.reinit()},r.prototype.setCSS=function(t){var e,i,s=this,o={};!0===s.options.rtl&&(t=-t),e="left"==s.positionProp?Math.ceil(t)+"px":"0px",i="top"==s.positionProp?Math.ceil(t)+"px":"0px",o[s.positionProp]=t,!1===s.transformsEnabled||(!(o={})===s.cssTransitions?o[s.animType]="translate("+e+", "+i+")":o[s.animType]="translate3d("+e+", "+i+", 0px)"),s.$slideTrack.css(o)},r.prototype.setDimensions=function(){var t=this;!1===t.options.vertical?!0===t.options.centerMode&&t.$list.css({padding:"0px "+t.options.centerPadding}):(t.$list.height(t.$slides.first().outerHeight(!0)*t.options.slidesToShow),!0===t.options.centerMode&&t.$list.css({padding:t.options.centerPadding+" 0px"})),t.listWidth=t.$list.width(),t.listHeight=t.$list.height(),!1===t.options.vertical&&!1===t.options.variableWidth?(t.slideWidth=Math.ceil(t.listWidth/t.options.slidesToShow),t.$slideTrack.width(Math.ceil(t.slideWidth*t.$slideTrack.children(".slick-slide").length))):!0===t.options.variableWidth?t.$slideTrack.width(5e3*t.slideCount):(t.slideWidth=Math.ceil(t.listWidth),t.$slideTrack.height(Math.ceil(t.$slides.first().outerHeight(!0)*t.$slideTrack.children(".slick-slide").length)));var e=t.$slides.first().outerWidth(!0)-t.$slides.first().width();!1===t.options.variableWidth&&t.$slideTrack.children(".slick-slide").width(t.slideWidth-e)},r.prototype.setFade=function(){var i,s=this;s.$slides.each(function(t,e){i=s.slideWidth*t*-1,!0===s.options.rtl?d(e).css({position:"relative",right:i,top:0,zIndex:s.options.zIndex-2,opacity:0}):d(e).css({position:"relative",left:i,top:0,zIndex:s.options.zIndex-2,opacity:0})}),s.$slides.eq(s.currentSlide).css({zIndex:s.options.zIndex-1,opacity:1})},r.prototype.setHeight=function(){var t=this;if(1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical){var e=t.$slides.eq(t.currentSlide).outerHeight(!0);t.$list.css("height",e)}},r.prototype.setOption=r.prototype.slickSetOption=function(){var t,e,i,s,o,n=this,r=!1;if("object"===d.type(arguments[0])?(i=arguments[0],r=arguments[1],o="multiple"):"string"===d.type(arguments[0])&&(s=arguments[1],r=arguments[2],"responsive"===(i=arguments[0])&&"array"===d.type(arguments[1])?o="responsive":void 0!==arguments[1]&&(o="single")),"single"===o)n.options[i]=s;else if("multiple"===o)d.each(i,function(t,e){n.options[t]=e});else if("responsive"===o)for(e in s)if("array"!==d.type(n.options.responsive))n.options.responsive=[s[e]];else{for(t=n.options.responsive.length-1;0<=t;)n.options.responsive[t].breakpoint===s[e].breakpoint&&n.options.responsive.splice(t,1),t--;n.options.responsive.push(s[e])}r&&(n.unload(),n.reinit())},r.prototype.setPosition=function(){var t=this;t.setDimensions(),t.setHeight(),!1===t.options.fade?t.setCSS(t.getLeft(t.currentSlide)):t.setFade(),t.$slider.trigger("setPosition",[t])},r.prototype.setProps=function(){var t=this,e=document.body.style;t.positionProp=!0===t.options.vertical?"top":"left","top"===t.positionProp?t.$slider.addClass("slick-vertical"):t.$slider.removeClass("slick-vertical"),void 0===e.WebkitTransition&&void 0===e.MozTransition&&void 0===e.msTransition||!0===t.options.useCSS&&(t.cssTransitions=!0),t.options.fade&&("number"==typeof t.options.zIndex?t.options.zIndex<3&&(t.options.zIndex=3):t.options.zIndex=t.defaults.zIndex),void 0!==e.OTransform&&(t.animType="OTransform",t.transformType="-o-transform",t.transitionType="OTransition",void 0===e.perspectiveProperty&&void 0===e.webkitPerspective&&(t.animType=!1)),void 0!==e.MozTransform&&(t.animType="MozTransform",t.transformType="-moz-transform",t.transitionType="MozTransition",void 0===e.perspectiveProperty&&void 0===e.MozPerspective&&(t.animType=!1)),void 0!==e.webkitTransform&&(t.animType="webkitTransform",t.transformType="-webkit-transform",t.transitionType="webkitTransition",void 0===e.perspectiveProperty&&void 0===e.webkitPerspective&&(t.animType=!1)),void 0!==e.msTransform&&(t.animType="msTransform",t.transformType="-ms-transform",t.transitionType="msTransition",void 0===e.msTransform&&(t.animType=!1)),void 0!==e.transform&&!1!==t.animType&&(t.animType="transform",t.transformType="transform",t.transitionType="transition"),t.transformsEnabled=t.options.useTransform&&null!==t.animType&&!1!==t.animType},r.prototype.setSlideClasses=function(t){var e,i,s,o,n=this;if(i=n.$slider.find(".slick-slide").removeClass("slick-active slick-center slick-current").attr("aria-hidden","true"),n.$slides.eq(t).addClass("slick-current"),!0===n.options.centerMode){var r=n.options.slidesToShow%2==0?1:0;e=Math.floor(n.options.slidesToShow/2),!0===n.options.infinite&&(e<=t&&t<=n.slideCount-1-e?n.$slides.slice(t-e+r,t+e+1).addClass("slick-active").attr("aria-hidden","false"):(s=n.options.slidesToShow+t,i.slice(s-e+1+r,s+e+2).addClass("slick-active").attr("aria-hidden","false")),0===t?i.eq(i.length-1-n.options.slidesToShow).addClass("slick-center"):t===n.slideCount-1&&i.eq(n.options.slidesToShow).addClass("slick-center")),n.$slides.eq(t).addClass("slick-center")}else 0<=t&&t<=n.slideCount-n.options.slidesToShow?n.$slides.slice(t,t+n.options.slidesToShow).addClass("slick-active").attr("aria-hidden","false"):i.length<=n.options.slidesToShow?i.addClass("slick-active").attr("aria-hidden","false"):(o=n.slideCount%n.options.slidesToShow,s=!0===n.options.infinite?n.options.slidesToShow+t:t,n.options.slidesToShow==n.options.slidesToScroll&&n.slideCount-t<n.options.slidesToShow?i.slice(s-(n.options.slidesToShow-o),s+o).addClass("slick-active").attr("aria-hidden","false"):i.slice(s,s+n.options.slidesToShow).addClass("slick-active").attr("aria-hidden","false"));"ondemand"!==n.options.lazyLoad&&"anticipated"!==n.options.lazyLoad||n.lazyLoad()},r.prototype.setupInfinite=function(){var t,e,i,s=this;if(!0===s.options.fade&&(s.options.centerMode=!1),!0===s.options.infinite&&!1===s.options.fade&&(e=null,s.slideCount>s.options.slidesToShow)){for(i=!0===s.options.centerMode?s.options.slidesToShow+1:s.options.slidesToShow,t=s.slideCount;t>s.slideCount-i;t-=1)e=t-1,d(s.$slides[e]).clone(!0).attr("id","").attr("data-slick-index",e-s.slideCount).prependTo(s.$slideTrack).addClass("slick-cloned");for(t=0;t<i+s.slideCount;t+=1)e=t,d(s.$slides[e]).clone(!0).attr("id","").attr("data-slick-index",e+s.slideCount).appendTo(s.$slideTrack).addClass("slick-cloned");s.$slideTrack.find(".slick-cloned").find("[id]").each(function(){d(this).attr("id","")})}},r.prototype.interrupt=function(t){t||this.autoPlay(),this.interrupted=t},r.prototype.selectHandler=function(t){var e=d(t.target).is(".slick-slide")?d(t.target):d(t.target).parents(".slick-slide"),i=parseInt(e.attr("data-slick-index"));i=i||0,this.slideCount<=this.options.slidesToShow?this.slideHandler(i,!1,!0):this.slideHandler(i)},r.prototype.slideHandler=function(t,e,i){var s,o,n,r,a,l,d=this;if(e=e||!1,!(!0===d.animating&&!0===d.options.waitForAnimate||!0===d.options.fade&&d.currentSlide===t))if(!1===e&&d.asNavFor(t),s=t,a=d.getLeft(s),r=d.getLeft(d.currentSlide),d.currentLeft=null===d.swipeLeft?r:d.swipeLeft,!1===d.options.infinite&&!1===d.options.centerMode&&(t<0||t>d.getDotCount()*d.options.slidesToScroll))!1===d.options.fade&&(s=d.currentSlide,!0!==i&&d.slideCount>d.options.slidesToShow?d.animateSlide(r,function(){d.postSlide(s)}):d.postSlide(s));else if(!1===d.options.infinite&&!0===d.options.centerMode&&(t<0||t>d.slideCount-d.options.slidesToScroll))!1===d.options.fade&&(s=d.currentSlide,!0!==i&&d.slideCount>d.options.slidesToShow?d.animateSlide(r,function(){d.postSlide(s)}):d.postSlide(s));else{if(d.options.autoplay&&clearInterval(d.autoPlayTimer),o=s<0?d.slideCount%d.options.slidesToScroll!=0?d.slideCount-d.slideCount%d.options.slidesToScroll:d.slideCount+s:s>=d.slideCount?d.slideCount%d.options.slidesToScroll!=0?0:s-d.slideCount:s,d.animating=!0,d.$slider.trigger("beforeChange",[d,d.currentSlide,o]),n=d.currentSlide,d.currentSlide=o,d.setSlideClasses(d.currentSlide),d.options.asNavFor&&(l=(l=d.getNavTarget()).slick("getSlick")).slideCount<=l.options.slidesToShow&&l.setSlideClasses(d.currentSlide),d.updateDots(),d.updateArrows(),!0===d.options.fade)return!0!==i?(d.fadeSlideOut(n),d.fadeSlide(o,function(){d.postSlide(o)})):d.postSlide(o),void d.animateHeight();!0!==i&&d.slideCount>d.options.slidesToShow?d.animateSlide(a,function(){d.postSlide(o)}):d.postSlide(o)}},r.prototype.startLoad=function(){var t=this;!0===t.options.arrows&&t.slideCount>t.options.slidesToShow&&(t.$prevArrow.hide(),t.$nextArrow.hide()),!0===t.options.dots&&t.slideCount>t.options.slidesToShow&&t.$dots.hide(),t.$slider.addClass("slick-loading")},r.prototype.swipeDirection=function(){var t,e,i,s,o=this;return t=o.touchObject.startX-o.touchObject.curX,e=o.touchObject.startY-o.touchObject.curY,i=Math.atan2(e,t),(s=Math.round(180*i/Math.PI))<0&&(s=360-Math.abs(s)),s<=45&&0<=s?!1===o.options.rtl?"left":"right":s<=360&&315<=s?!1===o.options.rtl?"left":"right":135<=s&&s<=225?!1===o.options.rtl?"right":"left":!0===o.options.verticalSwiping?35<=s&&s<=135?"down":"up":"vertical"},r.prototype.swipeEnd=function(t){var e,i,s=this;if(s.dragging=!1,s.swiping=!1,s.scrolling)return s.scrolling=!1;if(s.interrupted=!1,s.shouldClick=!(10<s.touchObject.swipeLength),void 0===s.touchObject.curX)return!1;if(!0===s.touchObject.edgeHit&&s.$slider.trigger("edge",[s,s.swipeDirection()]),s.touchObject.swipeLength>=s.touchObject.minSwipe){switch(i=s.swipeDirection()){case"left":case"down":e=s.options.swipeToSlide?s.checkNavigable(s.currentSlide+s.getSlideCount()):s.currentSlide+s.getSlideCount(),s.currentDirection=0;break;case"right":case"up":e=s.options.swipeToSlide?s.checkNavigable(s.currentSlide-s.getSlideCount()):s.currentSlide-s.getSlideCount(),s.currentDirection=1}"vertical"!=i&&(s.slideHandler(e),s.touchObject={},s.$slider.trigger("swipe",[s,i]))}else s.touchObject.startX!==s.touchObject.curX&&(s.slideHandler(s.currentSlide),s.touchObject={})},r.prototype.swipeHandler=function(t){var e=this;if(!(!1===e.options.swipe||"ontouchend"in document&&!1===e.options.swipe||!1===e.options.draggable&&-1!==t.type.indexOf("mouse")))switch(e.touchObject.fingerCount=t.originalEvent&&void 0!==t.originalEvent.touches?t.originalEvent.touches.length:1,e.touchObject.minSwipe=e.listWidth/e.options.touchThreshold,!0===e.options.verticalSwiping&&(e.touchObject.minSwipe=e.listHeight/e.options.touchThreshold),t.data.action){case"start":e.swipeStart(t);break;case"move":e.swipeMove(t);break;case"end":e.swipeEnd(t)}},r.prototype.swipeMove=function(t){var e,i,s,o,n,r,a=this;return n=void 0!==t.originalEvent?t.originalEvent.touches:null,!(!a.dragging||a.scrolling||n&&1!==n.length)&&(e=a.getLeft(a.currentSlide),a.touchObject.curX=void 0!==n?n[0].pageX:t.clientX,a.touchObject.curY=void 0!==n?n[0].pageY:t.clientY,a.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(a.touchObject.curX-a.touchObject.startX,2))),r=Math.round(Math.sqrt(Math.pow(a.touchObject.curY-a.touchObject.startY,2))),!a.options.verticalSwiping&&!a.swiping&&4<r?!(a.scrolling=!0):(!0===a.options.verticalSwiping&&(a.touchObject.swipeLength=r),i=a.swipeDirection(),void 0!==t.originalEvent&&4<a.touchObject.swipeLength&&(a.swiping=!0,t.preventDefault()),o=(!1===a.options.rtl?1:-1)*(a.touchObject.curX>a.touchObject.startX?1:-1),!0===a.options.verticalSwiping&&(o=a.touchObject.curY>a.touchObject.startY?1:-1),s=a.touchObject.swipeLength,(a.touchObject.edgeHit=!1)===a.options.infinite&&(0===a.currentSlide&&"right"===i||a.currentSlide>=a.getDotCount()&&"left"===i)&&(s=a.touchObject.swipeLength*a.options.edgeFriction,a.touchObject.edgeHit=!0),!1===a.options.vertical?a.swipeLeft=e+s*o:a.swipeLeft=e+s*(a.$list.height()/a.listWidth)*o,!0===a.options.verticalSwiping&&(a.swipeLeft=e+s*o),!0!==a.options.fade&&!1!==a.options.touchMove&&(!0===a.animating?(a.swipeLeft=null,!1):void a.setCSS(a.swipeLeft))))},r.prototype.swipeStart=function(t){var e,i=this;if(i.interrupted=!0,1!==i.touchObject.fingerCount||i.slideCount<=i.options.slidesToShow)return!(i.touchObject={});void 0!==t.originalEvent&&void 0!==t.originalEvent.touches&&(e=t.originalEvent.touches[0]),i.touchObject.startX=i.touchObject.curX=void 0!==e?e.pageX:t.clientX,i.touchObject.startY=i.touchObject.curY=void 0!==e?e.pageY:t.clientY,i.dragging=!0},r.prototype.unfilterSlides=r.prototype.slickUnfilter=function(){var t=this;null!==t.$slidesCache&&(t.unload(),t.$slideTrack.children(this.options.slide).detach(),t.$slidesCache.appendTo(t.$slideTrack),t.reinit())},r.prototype.unload=function(){var t=this;d(".slick-cloned",t.$slider).remove(),t.$dots&&t.$dots.remove(),t.$prevArrow&&t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.remove(),t.$nextArrow&&t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.remove(),t.$slides.removeClass("slick-slide slick-active slick-visible slick-current").attr("aria-hidden","true").css("width","")},r.prototype.unslick=function(t){this.$slider.trigger("unslick",[this,t]),this.destroy()},r.prototype.updateArrows=function(){var t=this;Math.floor(t.options.slidesToShow/2),!0===t.options.arrows&&t.slideCount>t.options.slidesToShow&&!t.options.infinite&&(t.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false"),t.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false"),0===t.currentSlide?(t.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true"),t.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false")):t.currentSlide>=t.slideCount-t.options.slidesToShow&&!1===t.options.centerMode?(t.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),t.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")):t.currentSlide>=t.slideCount-1&&!0===t.options.centerMode&&(t.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),t.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")))},r.prototype.updateDots=function(){var t=this;null!==t.$dots&&(t.$dots.find("li").removeClass("slick-active").end(),t.$dots.find("li").eq(Math.floor(t.currentSlide/t.options.slidesToScroll)).addClass("slick-active"))},r.prototype.visibility=function(){this.options.autoplay&&(document[this.hidden]?this.interrupted=!0:this.interrupted=!1)},d.fn.slick=function(){var t,e,i=this,s=arguments[0],o=Array.prototype.slice.call(arguments,1),n=i.length;for(t=0;t<n;t++)if("object"==typeof s||void 0===s?i[t].slick=new r(i[t],s):e=i[t].slick[s].apply(i[t].slick,o),void 0!==e)return e;return i}}),function(){"use strict";function e(t){if(!t)throw new Error("No options passed to Waypoint constructor");if(!t.element)throw new Error("No element option passed to Waypoint constructor");if(!t.handler)throw new Error("No handler option passed to Waypoint constructor");this.key="waypoint-"+i,this.options=e.Adapter.extend({},e.defaults,t),this.element=this.options.element,this.adapter=new e.Adapter(this.element),this.callback=t.handler,this.axis=this.options.horizontal?"horizontal":"vertical",this.enabled=this.options.enabled,this.triggerPoint=null,this.group=e.Group.findOrCreate({name:this.options.group,axis:this.axis}),this.context=e.Context.findOrCreateByElement(this.options.context),e.offsetAliases[this.options.offset]&&(this.options.offset=e.offsetAliases[this.options.offset]),this.group.add(this),this.context.add(this),n[this.key]=this,i+=1}var i=0,n={};e.prototype.queueTrigger=function(t){this.group.queueTrigger(this,t)},e.prototype.trigger=function(t){this.enabled&&this.callback&&this.callback.apply(this,t)},e.prototype.destroy=function(){this.context.remove(this),this.group.remove(this),delete n[this.key]},e.prototype.disable=function(){return this.enabled=!1,this},e.prototype.enable=function(){return this.context.refresh(),this.enabled=!0,this},e.prototype.next=function(){return this.group.next(this)},e.prototype.previous=function(){return this.group.previous(this)},e.invokeAll=function(t){var e=[];for(var i in n)e.push(n[i]);for(var s=0,o=e.length;s<o;s++)e[s][t]()},e.destroyAll=function(){e.invokeAll("destroy")},e.disableAll=function(){e.invokeAll("disable")},e.enableAll=function(){for(var t in e.Context.refreshAll(),n)n[t].enabled=!0;return this},e.refreshAll=function(){e.Context.refreshAll()},e.viewportHeight=function(){return window.innerHeight||document.documentElement.clientHeight},e.viewportWidth=function(){return document.documentElement.clientWidth},e.adapters=[],e.defaults={context:window,continuous:!0,enabled:!0,group:"default",horizontal:!1,offset:0},e.offsetAliases={"bottom-in-view":function(){return this.context.innerHeight()-this.adapter.outerHeight()},"right-in-view":function(){return this.context.innerWidth()-this.adapter.outerWidth()}},window.Waypoint=e}(),function(){"use strict";function e(t){window.setTimeout(t,1e3/60)}function i(t){this.element=t,this.Adapter=m.Adapter,this.adapter=new this.Adapter(t),this.key="waypoint-context-"+s,this.didScroll=!1,this.didResize=!1,this.oldScroll={x:this.adapter.scrollLeft(),y:this.adapter.scrollTop()},this.waypoints={vertical:{},horizontal:{}},t.waypointContextKey=this.key,o[t.waypointContextKey]=this,s+=1,m.windowContext||(m.windowContext=!0,m.windowContext=new i(window)),this.createThrottledScrollHandler(),this.createThrottledResizeHandler()}var s=0,o={},m=window.Waypoint,t=window.onload;i.prototype.add=function(t){var e=t.options.horizontal?"horizontal":"vertical";this.waypoints[e][t.key]=t,this.refresh()},i.prototype.checkEmpty=function(){var t=this.Adapter.isEmptyObject(this.waypoints.horizontal),e=this.Adapter.isEmptyObject(this.waypoints.vertical),i=this.element==this.element.window;t&&e&&!i&&(this.adapter.off(".waypoints"),delete o[this.key])},i.prototype.createThrottledResizeHandler=function(){function t(){e.handleResize(),e.didResize=!1}var e=this;this.adapter.on("resize.waypoints",function(){e.didResize||(e.didResize=!0,m.requestAnimationFrame(t))})},i.prototype.createThrottledScrollHandler=function(){function t(){e.handleScroll(),e.didScroll=!1}var e=this;this.adapter.on("scroll.waypoints",function(){e.didScroll&&!m.isTouch||(e.didScroll=!0,m.requestAnimationFrame(t))})},i.prototype.handleResize=function(){m.Context.refreshAll()},i.prototype.handleScroll=function(){var t={},e={horizontal:{newScroll:this.adapter.scrollLeft(),oldScroll:this.oldScroll.x,forward:"right",backward:"left"},vertical:{newScroll:this.adapter.scrollTop(),oldScroll:this.oldScroll.y,forward:"down",backward:"up"}};for(var i in e){var s=e[i],o=s.newScroll>s.oldScroll?s.forward:s.backward;for(var n in this.waypoints[i]){var r=this.waypoints[i][n];if(null!==r.triggerPoint){var a=s.oldScroll<r.triggerPoint,l=s.newScroll>=r.triggerPoint;(a&&l||!a&&!l)&&(r.queueTrigger(o),t[r.group.id]=r.group)}}}for(var d in t)t[d].flushTriggers();this.oldScroll={x:e.horizontal.newScroll,y:e.vertical.newScroll}},i.prototype.innerHeight=function(){return this.element==this.element.window?m.viewportHeight():this.adapter.innerHeight()},i.prototype.remove=function(t){delete this.waypoints[t.axis][t.key],this.checkEmpty()},i.prototype.innerWidth=function(){return this.element==this.element.window?m.viewportWidth():this.adapter.innerWidth()},i.prototype.destroy=function(){var t=[];for(var e in this.waypoints)for(var i in this.waypoints[e])t.push(this.waypoints[e][i]);for(var s=0,o=t.length;s<o;s++)t[s].destroy()},i.prototype.refresh=function(){var t,e=this.element==this.element.window,i=e?void 0:this.adapter.offset(),s={};for(var o in this.handleScroll(),t={horizontal:{contextOffset:e?0:i.left,contextScroll:e?0:this.oldScroll.x,contextDimension:this.innerWidth(),oldScroll:this.oldScroll.x,forward:"right",backward:"left",offsetProp:"left"},vertical:{contextOffset:e?0:i.top,contextScroll:e?0:this.oldScroll.y,contextDimension:this.innerHeight(),oldScroll:this.oldScroll.y,forward:"down",backward:"up",offsetProp:"top"}}){var n=t[o];for(var r in this.waypoints[o]){var a,l,d,h,c=this.waypoints[o][r],u=c.options.offset,p=c.triggerPoint,f=0,g=null==p;c.element!==c.element.window&&(f=c.adapter.offset()[n.offsetProp]),"function"==typeof u?u=u.apply(c):"string"==typeof u&&(u=parseFloat(u),-1<c.options.offset.indexOf("%")&&(u=Math.ceil(n.contextDimension*u/100))),a=n.contextScroll-n.contextOffset,c.triggerPoint=Math.floor(f+a-u),l=p<n.oldScroll,d=c.triggerPoint>=n.oldScroll,h=!l&&!d,!g&&(l&&d)?(c.queueTrigger(n.backward),s[c.group.id]=c.group):!g&&h?(c.queueTrigger(n.forward),s[c.group.id]=c.group):g&&n.oldScroll>=c.triggerPoint&&(c.queueTrigger(n.forward),s[c.group.id]=c.group)}}return m.requestAnimationFrame(function(){for(var t in s)s[t].flushTriggers()}),this},i.findOrCreateByElement=function(t){return i.findByElement(t)||new i(t)},i.refreshAll=function(){for(var t in o)o[t].refresh()},i.findByElement=function(t){return o[t.waypointContextKey]},window.onload=function(){t&&t(),i.refreshAll()},m.requestAnimationFrame=function(t){(window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||e).call(window,t)},m.Context=i}(),function(){"use strict";function r(t,e){return t.triggerPoint-e.triggerPoint}function a(t,e){return e.triggerPoint-t.triggerPoint}function e(t){this.name=t.name,this.axis=t.axis,this.id=this.name+"-"+this.axis,this.waypoints=[],this.clearTriggerQueues(),i[this.axis][this.name]=this}var i={vertical:{},horizontal:{}},s=window.Waypoint;e.prototype.add=function(t){this.waypoints.push(t)},e.prototype.clearTriggerQueues=function(){this.triggerQueues={up:[],down:[],left:[],right:[]}},e.prototype.flushTriggers=function(){for(var t in this.triggerQueues){var e=this.triggerQueues[t],i="up"===t||"left"===t;e.sort(i?a:r);for(var s=0,o=e.length;s<o;s+=1){var n=e[s];!n.options.continuous&&s!==e.length-1||n.trigger([t])}}this.clearTriggerQueues()},e.prototype.next=function(t){this.waypoints.sort(r);var e=s.Adapter.inArray(t,this.waypoints);return e===this.waypoints.length-1?null:this.waypoints[e+1]},e.prototype.previous=function(t){this.waypoints.sort(r);var e=s.Adapter.inArray(t,this.waypoints);return e?this.waypoints[e-1]:null},e.prototype.queueTrigger=function(t,e){this.triggerQueues[e].push(t)},e.prototype.remove=function(t){var e=s.Adapter.inArray(t,this.waypoints);-1<e&&this.waypoints.splice(e,1)},e.prototype.first=function(){return this.waypoints[0]},e.prototype.last=function(){return this.waypoints[this.waypoints.length-1]},e.findOrCreate=function(t){return i[t.axis][t.name]||new e(t)},s.Group=e}(),function(){"use strict";function i(t){this.$element=s(t)}var s=window.jQuery,t=window.Waypoint;s.each(["innerHeight","innerWidth","off","offset","on","outerHeight","outerWidth","scrollLeft","scrollTop"],function(t,e){i.prototype[e]=function(){var t=Array.prototype.slice.call(arguments);return this.$element[e].apply(this.$element,t)}}),s.each(["extend","inArray","isEmptyObject"],function(t,e){i[e]=s[e]}),t.adapters.push({name:"jquery",Adapter:i}),t.Adapter=i}(),function(){"use strict";function t(s){return function(){var e=[],i=arguments[0];return s.isFunction(arguments[0])&&((i=s.extend({},arguments[1])).handler=arguments[0]),this.each(function(){var t=s.extend({},i,{element:this});"string"==typeof t.context&&(t.context=s(this).closest(t.context)[0]),e.push(new o(t))}),e}}var o=window.Waypoint;window.jQuery&&(window.jQuery.fn.waypoint=t(window.jQuery)),window.Zepto&&(window.Zepto.fn.waypoint=t(window.Zepto))}();