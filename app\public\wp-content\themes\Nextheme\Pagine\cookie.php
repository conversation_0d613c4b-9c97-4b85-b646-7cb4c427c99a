<?php
/**
 * Template Name: Cookie Policy
 * Template per la pagina Cookie Policy
 */

// Imposto il titolo della pagina
add_filter('pre_get_document_title', function() {
    return 'Cookie Policy - Movimento Terra Pacitto';
});

// Aggiungo meta tag per impedire l'indicizzazione
add_action('wp_head', function() {
    echo '<meta name="robots" content="noindex, nofollow">';
});

// Il CSS per questa pagina viene caricato tramite la funzione nextnaked_policy_pages_styles in functions.php

// Use custom policy header instead of standard header
include(get_template_directory() . '/policy-header.php');

// Inclusione diretta del CSS per sicurezza
echo '<link rel="stylesheet" href="' . get_template_directory_uri() . '/assets/css/policy-pages.css" type="text/css" media="all" />';

// Aggiungi stile inline per forzare il padding corretto e superare eventuali problemi di cache
echo '<style>
    .cookie-page { padding: 0 !important; }
    @media (max-width: 768px) { .cookie-page { padding: 0 !important; } }

    /* Fix per il breadcrumb buggato in ambiente pubblico */
    .cookie-breadcrumb {
        background-color: #2B2D37 !important;
        padding: 20px 0 30px 0 !important;
        margin-bottom: 0 !important;
        width: 100% !important;
        display: block !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .cookie-breadcrumb .container {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 15px !important;
        display: block !important;
        border: none !important;
        border-bottom: none !important;
        border-top: none !important;
        position: relative !important;
    }

    /* Rimuovi qualsiasi linea o separatore che potrebbe apparire nel container */
    .cookie-breadcrumb .container:after,
    .cookie-breadcrumb .container:before {
        display: none !important;
        content: none !important;
        border: none !important;
        background: none !important;
    }

    .cookie-breadcrumb h2 {
        color: #fff !important;
        font-size: 32px !important;
        margin-bottom: 10px !important;
        font-weight: 600 !important;
        display: block !important;
        border: none !important;
        border-bottom: none !important;
        padding-bottom: 0 !important;
        position: relative !important;
    }

    /* Rimuovi qualsiasi linea o separatore che potrebbe apparire */
    .cookie-breadcrumb h2:after,
    .cookie-breadcrumb h2:before {
        display: none !important;
        content: none !important;
        border: none !important;
        background: none !important;
    }

    .cookie-breadcrumb .breadcrumb-list {
        display: flex !important;
        list-style: none !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        border-top: none !important;
        border-bottom: none !important;
        position: relative !important;
    }

    /* Rimuovi qualsiasi linea o separatore che potrebbe apparire nella lista */
    .cookie-breadcrumb .breadcrumb-list:after,
    .cookie-breadcrumb .breadcrumb-list:before {
        display: none !important;
        content: none !important;
        border: none !important;
        background: none !important;
    }

    .cookie-breadcrumb .breadcrumb-item {
        color: #FABC3D !important;
        font-size: 16px !important;
    }

    .cookie-breadcrumb .breadcrumb-item a {
        color: #fff !important;
        text-decoration: none !important;
        transition: all 0.3s ease !important;
    }

    .cookie-breadcrumb .breadcrumb-item a:hover {
        color: #FABC3D !important;
    }

    .cookie-breadcrumb .breadcrumb-item + .breadcrumb-item:before {
        content: "/" !important;
        padding: 0 10px !important;
        color: #fff !important;
    }

    @media (max-width: 768px) {
        .cookie-breadcrumb {
            padding: 10px 0 20px 0 !important;
        }

        .cookie-breadcrumb h2 {
            font-size: 24px !important;
        }
    }
</style>';
?>

<!-- Breadcrumb Section Start -->
<div class="cookie-breadcrumb">
    <div class="container" data-aos="fade-down" data-aos-duration="600">
        <h2 style="color: #fff; font-size: 32px; margin-bottom: 10px; font-weight: 600; border: none; padding-bottom: 0;">Cookie Policy</h2>
        <div class="breadcrumb-list" style="display: flex; list-style: none; margin: 0; padding: 0; border: none;">
            <span style="color: #fff; font-size: 16px; border: none; text-decoration: none;"><a href="<?php echo home_url(); ?>" style="color: #fff; text-decoration: none; border: none;">Home</a></span>
            <span style="padding: 0 10px; color: #fff;">/</span>
            <span style="color: #FABC3D; font-size: 16px; border: none; text-decoration: none;">Cookie Policy</span>
        </div>
    </div>
</div>
<!-- Breadcrumb Section End -->

<main class="cookie-page">
    <div class="containers" data-aos="fade-up" data-aos-duration="800">
        <h1>Politica dei Cookie di <span>Movimento Terra Pacitto</span></h1>

        <div class="section intro-section">
            <p class="last-update"><strong>Ultimo aggiornamento:</strong> 23/09/2024</p>
            <p>Questa Politica dei Cookie è conforme al Regolamento Generale sulla Protezione dei Dati (GDPR) dell'UE.</p>
        </div>

        <div class="section">
            <h2>1. Introduzione</h2>
            <p>Movimento Terra Pacitto utilizza cookie per migliorare l'esperienza degli utenti sul nostro sito web. Questa politica spiega come utilizziamo i cookie e le opzioni a vostra disposizione.</p>
        </div>

        <div class="section">
            <h2>2. Cosa sono i cookie?</h2>
            <p>I cookie sono piccoli file di testo che vengono salvati sul vostro dispositivo quando visitate il nostro sito web. Questi file consentono al sito di ricordare le vostre azioni e preferenze per un determinato periodo di tempo.</p>
        </div>

        <div class="section">
            <h2>3. Tipi di cookie che utilizziamo</h2>
            <p>Utilizziamo i seguenti tipi di cookie:</p>
            <ul>
                <li><strong>Cookie strettamente necessari:</strong> Essenziali per il funzionamento del sito. Non possono essere disattivati.</li>
                <li><strong>Cookie analitici/prestazionali:</strong> Ci aiutano a capire come i visitatori interagiscono con il nostro sito, consentendoci di migliorare funzionalità e contenuti.</li>
                <li><strong>Cookie funzionali:</strong> Permettono al sito di ricordare le scelte che fate (come il vostro nome utente o la regione in cui vi trovate) e fornire funzionalità migliorate e personalizzate.</li>
            </ul>
        </div>

        <div class="section">
            <h2>4. Base giuridica per l'utilizzo dei cookie</h2>
            <p>Utilizziamo i cookie strettamente necessari sulla base del nostro legittimo interesse a fornire un sito web funzionante. Per altri tipi di cookie, richiediamo il vostro consenso prima di installarli sul vostro dispositivo.</p>
        </div>

        <div class="section">
            <h2>5. Come gestire i cookie</h2>
            <p>Puoi gestire i cookie attraverso le impostazioni del tuo browser. Di seguito sono riportati i link alle guide su come gestire i cookie nei browser più comuni:</p>
            <ul>
                <li><a href="https://support.google.com/chrome/answer/95647?hl=it" target="_blank">Google Chrome</a></li>
                <li><a href="https://support.mozilla.org/it/kb/Gestione%20dei%20cookie" target="_blank">Mozilla Firefox</a></li>
                <li><a href="https://support.apple.com/it-it/guide/safari/sfri11471/mac" target="_blank">Apple Safari</a></li>
                <li><a href="https://support.microsoft.com/it-it/help/4027947/microsoft-edge-delete-cookies" target="_blank">Microsoft Edge</a></li>
            </ul>
        </div>

        <div class="section">
            <h2>6. I vostri diritti</h2>
            <p>Hai il diritto di accedere, rettificare, cancellare e limitare il trattamento dei tuoi dati personali. Per esercitare questi diritti, contattaci tramite i nostri canali ufficiali di comunicazione indicati nella sezione "Contatti".</p>
        </div>

        <div class="section">
            <h2>7. Contatti e reclami</h2>
            <p>Per qualsiasi domanda riguardante la nostra Politica dei Cookie o per presentare un reclamo, puoi contattarci tramite il nostro modulo di contatto o all'indirizzo email indicato nella sezione contatti del nostro sito.</p>
        </div>

        <div class="section">
            <h2>8. Modifiche alla Politica dei Cookie</h2>
            <p>Ci riserviamo il diritto di modificare questa politica in qualsiasi momento. Le modifiche saranno pubblicate su questa pagina con una nuova data di aggiornamento.</p>
        </div>

        <div class="section">
            <h2>9. Cookie di terze parti</h2>
            <p>Il nostro sito potrebbe utilizzare servizi di terze parti, come Google Analytics, per analizzare il traffico del sito. Questi servizi potrebbero utilizzare i propri cookie, che sono soggetti alle loro politiche sulla privacy.</p>
        </div>

        <div class="section highlight" data-aos="fade-up" data-aos-delay="200">
            <h2>10. Contatti</h2>
            <p>Per qualsiasi domanda o richiesta riguardante questa politica dei cookie o il trattamento dei dati personali, si prega di contattare:</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span class="contact-label">Email:</span>
                    <span class="contact-value"><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span class="contact-label">Telefono:</span>
                    <span class="contact-value">0775 408250</span>
                </div>
                <div class="contact-item">
                    <span class="contact-label">Indirizzo:</span>
                    <span class="contact-value">Via Campello, 57<br>03011 Mole Bisleti Alatri (FR)</span>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>

<script>
    // Reinizializza AOS dopo il caricamento completo della pagina
    document.addEventListener('DOMContentLoaded', function() {
        // Controlla se AOS è disponibile
        if (typeof AOS !== 'undefined') {
            // Reinizializza AOS
            AOS.refresh();
        }
    });
</script>
