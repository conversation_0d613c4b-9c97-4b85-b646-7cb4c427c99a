/*!
 * justifiedGallery - v4.0.0-alpha
 * http://miromannino.github.io/Justified-Gallery/
 * Copyright (c) 2019 <PERSON><PERSON>
 * Licensed under the MIT license.
 */.justified-gallery{width:100%;position:relative;overflow:hidden}.justified-gallery>a,.justified-gallery>div,.justified-gallery>figure{position:absolute;display:inline-block;overflow:hidden;filter:"alpha(opacity=10)";opacity:.1;margin:0;padding:0}.justified-gallery>a>a>img,.justified-gallery>a>a>svg,.justified-gallery>a>img,.justified-gallery>a>svg,.justified-gallery>div>a>img,.justified-gallery>div>a>svg,.justified-gallery>div>img,.justified-gallery>div>svg,.justified-gallery>figure>a>img,.justified-gallery>figure>a>svg,.justified-gallery>figure>img,.justified-gallery>figure>svg{position:absolute;top:50%;left:50%;margin:0;padding:0;border:none;filter:"alpha(opacity=0)";opacity:0}.justified-gallery>a>.caption,.justified-gallery>div>.caption,.justified-gallery>figure>.caption{display:none;position:absolute;bottom:0;padding:5px;background-color:#000;left:0;right:0;margin:0;color:#fff;font-size:12px;font-weight:300;font-family:sans-serif}.justified-gallery>a>.caption.caption-visible,.justified-gallery>div>.caption.caption-visible,.justified-gallery>figure>.caption.caption-visible{display:initial;filter:"alpha(opacity=70)";opacity:.7;-webkit-transition:opacity .5s ease-in;-moz-transition:opacity .5s ease-in;-o-transition:opacity .5s ease-in;transition:opacity .5s ease-in}.justified-gallery>.jg-entry-visible{filter:"alpha(opacity=100)";opacity:1;background:0 0}.justified-gallery>.jg-entry-visible>a>img,.justified-gallery>.jg-entry-visible>a>svg,.justified-gallery>.jg-entry-visible>img,.justified-gallery>.jg-entry-visible>svg{filter:"alpha(opacity=100)";opacity:1;-webkit-transition:opacity .5s ease-in;-moz-transition:opacity .5s ease-in;-o-transition:opacity .5s ease-in;transition:opacity .5s ease-in}.justified-gallery>.jg-filtered{display:none}.justified-gallery>.spinner{position:absolute;bottom:0;margin-left:-24px;padding:10px 0 10px 0;left:50%;filter:"alpha(opacity=100)";opacity:1;overflow:initial}.justified-gallery>.spinner>span{display:inline-block;filter:"alpha(opacity=0)";opacity:0;width:8px;height:8px;margin:0 4px 0 4px;background-color:#000;border-radius:6px}